declare namespace Api.GetInspectCount{
  export interface Request {
    /**
     * 库存细码id
     */
    stock_product_detail_id?: number
    [property: string]: any
  }
  /**
   * product.GetInspectCountData
   */
  export interface Response {
    /**
     * 缸号
     */
    dyelot_number?: string
    /**
     * 抽查匹数 (检验)
     */
    inspect_total_roll?: number
    /**
     * 缸匹数
     */
    total_roll?: number
    [property: string]: any
  }
}
