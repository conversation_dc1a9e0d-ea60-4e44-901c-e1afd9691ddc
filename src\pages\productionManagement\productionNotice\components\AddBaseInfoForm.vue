<script setup lang="ts">
import { nextTick, onMounted, reactive, ref, watch, withDefaults } from 'vue'
import SelectProPlanOrder from './selectProPlanOrder.vue'
import { BusinessUnitIdEnum, EmployeeType } from '@/common/enum'
import { formatDate } from '@/common/format'
import { PHONE_REGEXP } from '@/common/rule'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import { getDefaultSaleSystem } from '@/common/util'
import SelectSettleTypeDialog from '@/components/SelectSettleTypeDialog/index.vue'

interface Props {
  type?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'create',
})

const emits = defineEmits([
  'getGreyInfoList',
  'selectSaleSystemId',
  'getFormData',
  'setOrderNo',
  'setSelectWatchStatus',
  'submitData',
  'selectCustomer',
])

const componentRemoteSearch = reactive({
  name: '',
  saleUserName: '',
  orderQcUserId: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
  follower_name: '',
})

const state = reactive<any>({
  saleInfo: false,
  formInline: {
    production_plan_order_id: '',
    production_plan_order_no: '',
    notify_date: new Date(),
    sale_system_id: '',
    weaving_mill_id: '',
    weaving_mill_name: '',
    weaving_mill_order_follower_id: '',
    weaving_mill_order_follower_phone: '',
    customer_id: '',
    sale_user_id: '',
    order_qc_user_id: '',
    payment_term: '',
    payment_term_name: '',
    receipt_grey_fabric_date: '',
    receipt_grey_fabric_address: '',
    invoice_header: '',
    order_remark: '',
    settle_type_id: '',
  },
  formRules: {
    weaving_mill_order_follower_phone: {
      pattern: PHONE_REGEXP,
      message: '手机号格式不对',
      trigger: 'blur',
    },
    notify_date: [
      {
        required: true,
        message: '请选择通知日期',
        trigger: 'change',
      },
    ],
    sale_system_id: [
      {
        required: true,
        message: '请选择营销体系',
        trigger: 'change',
        type: 'number',
      },
    ],
    weaving_mill_id: [
      {
        required: true,
        message: '请选择织厂名称',
        trigger: 'change',
        type: 'number',
      },
    ],
    customer_id: [
      {
        required: true,
        message: '请选择客户名称',
        trigger: 'change',
        type: 'number',
      },
    ],
    // order_qc_user_id: [
    //   {
    //     required: true,
    //     message: '请选择跟单QC员',
    //     trigger: 'change',
    //     type: 'number',
    //   },
    // ],
  },
})
//   日期选择器
function disabledDate(time: Date) {
  return time.getTime() + 24 * 60 * 60 * 1000 < Date.now()
}

const customerRef = ref()
function selectSaleSystemId(item: any, flag = true) {
  if (flag) {
    state.formInline.customer_id = ''
    customerRef.value.inputLabel = ''
  }
  emits('selectSaleSystemId', item, state.formInline.production_plan_order_id)
}

// function getWeaving(val: any) {
//   // state.formInline.weaving_mill_order_follower_id = val
//   state.formInline.receipt_grey_fabric_address = val?.address
//   state.formInline.weaving_mill_order_follower_phone = val?.phone
//   state.formInline.weaving_mill_order_follower_id = val?.order_follower_id || 0
//   state.formInline.order_qc_user_id = val?.order_qc_user_id || 0
// }

const SelectProPlanOrderRef = ref()
function showSelectProPlanOrder() {
  SelectProPlanOrderRef.value.state.showModal = true
}
// 选中生产计划单号
function changeProPlanOrder(item: any) {
  // let arr: any[] = []

  if (item) {
    // 自动带出
    // 计划单号 营销体系 织厂名称 织厂跟单 跟单电话 交坯日期 收坯地址
    state.formInline.customer_id = item.customer_id

    nextTick(() => {
      customerRef.value.getData()
    })
    state.formInline.production_plan_order_id = item.id || ''
    state.formInline.production_plan_order_no = item.order_no || ''
    state.formInline.sale_system_id = item.sale_system_id || ''
    state.formInline.weaving_mill_id = item.weaving_mill_id || ''
    state.formInline.weaving_mill_order_follower_id
      = item.weaving_mill_order_follower_id || ''
    // state.formInline.weaving_mill_order_follower_phone = item.biz_unit_order_follower_phone || ''
    state.formInline.receipt_grey_fabric_date = item.receipt_grey_fabric_date
    state.formInline.receipt_grey_fabric_address
      = item.receipt_grey_fabric_address
  }
  else {
    state.formInline.customer_id = ''
    state.formInline.production_plan_order_no = ''
    state.formInline.production_plan_order_id = ''
    state.formInline.sale_system_id = ''
    state.formInline.weaving_mill_id = ''
    state.formInline.weaving_mill_order_follower_id = ''
    // state.formInline.weaving_mill_order_follower_phone = ''
    state.formInline.receipt_grey_fabric_date = ''
    state.formInline.receipt_grey_fabric_address = ''
  }
  emits('setOrderNo', item?.order_no)
  emits('getGreyInfoList', [item], {
    sale_system_id: item.sale_system_id,
    production_plan_order_id: item.id,
  })
}

const formRef = ref()

async function submitData() {
  const form = {
    ...state.formInline,
    notify_date: state.formInline.notify_date
      ? formatDate(state.formInline.notify_date)
      : '',
    receipt_grey_fabric_date: state.formInline.receipt_grey_fabric_date
      ? formatDate(state.formInline.receipt_grey_fabric_date)
      : '',
  }
  // await formRef.value.validate((valid: boolean) => {
  //   form = valid
  //     ? {
  //         ...state.formInline,
  //         notify_date: state.formInline.notify_date ? formatDate(state.formInline.notify_date) : '',
  //         receipt_grey_fabric_date: state.formInline.receipt_grey_fabric_date ? formatDate(state.formInline.receipt_grey_fabric_date) : '',
  //       }
  //     : {}

  emits('getFormData', form)
  // return { reg: valid }
  //

  // })
}

watch(
  () => state.formInline,
  () => {
    emits('getFormData', {
      ...state.formInline,
      notify_date: state.formInline.notify_date
        ? formatDate(state.formInline.notify_date)
        : '',
      receipt_grey_fabric_date: state.formInline.receipt_grey_fabric_date
        ? formatDate(state.formInline.receipt_grey_fabric_date)
        : '',
    })
  },
  { deep: true },
)

function setPaymentTermName(item: any) {
  state.formInline.payment_term_name = item.name
}

// 回显数据
function setData(objData: any) {
  const keys = [
    'production_plan_order_id',
    'notify_date',
    'sale_system_id',
    'weaving_mill_id',
    'weaving_mill_name',
    'weaving_mill_order_follower_id',
    'weaving_mill_order_follower_phone',
    'customer_id',
    'sale_user_id',
    'order_qc_user_id',
    'payment_term',
    'payment_term_name',
    'receipt_grey_fabric_date',
    'receipt_grey_fabric_address',
    'invoice_header',
    'order_remark',
    'customer_order_follower_id',
    'customer_name',
    'settle_type_id',
  ]
  keys.forEach((key: string) => {
    if (objData[key])
      state.formInline[key] = objData[key]
  })
  const {
    sale_system_id = 0,
    production_plan_order_no = '',
    customer_name = '',
  } = objData
  customerRef.value.inputLabel = customer_name
  // state.formInline.order_qc_user_id = String(state.formInline.order_qc_user_id)
  state.formInline.production_plan_order_no = production_plan_order_no
  selectSaleSystemId({ sale_system_id, id: sale_system_id }, false)
  emits('setSelectWatchStatus', true)

  // state.formInline.production_plan_order_id = objData.production_plan_order_id
  // state.formInline.notify_date = objData.notify_date
  // state.formInline.weaving_mill_id = objData.weaving_mill_id
  // state.formInline.sale_system_id = objData.sale_system_id
  // state.formInline.weaving_mill_order_follower_id = objData.weaving_mill_order_follower_id
}
// 销售计划单带出客户和销售员
function setForm(form: any) {
  state.saleInfo = true
  if (form?.customer_id)
    state.formInline.customer_id = form?.customer_id

  state.formInline.sale_user_id
    = form.sale_user_id === undefined
      ? state.formInline.sale_user_id
      : form.sale_user_id
}

const followerRef = ref()
// 带出销售员
function changeCustomer(item: any) {
  followerRef.value.inputLabel = item.order_follower_name
  state.formInline.customer_order_follower_id = item.order_follower_id
  state.formInline.sale_user_id = item?.seller_id || ''
  state.formInline.customer_id = item.id
  state.formInline.customer_name = item.name
  emits('selectCustomer', item)
}
// 带出QC员
function handleChangeValue(item: any) {
  state.formInline.order_qc_user_id = item?.order_qc_user_id || ''
}

function changeFollowerId(item: any) {
  if ((props.type === 'edit' && !item?.initFirst) || props.type === 'create')
    state.formInline.weaving_mill_order_follower_phone = item?.phone || ''
}
function submitAllData() {
  emits('submitData')
}

onMounted(() => {
  if (props.type === 'create') {
    // 获取用户上的默认营销体系
    const res = getDefaultSaleSystem()

    if (res) {
      state.formInline.sale_system_id = res.default_sale_system_id
      selectSaleSystemId(
        {
          sale_system_id: res.default_sale_system_id,
          id: res.default_sale_system_id,
        },
        false,
      )
    }
  }
})
defineExpose({
  submitData,
  setData,
  setForm,
})
</script>

<template>
  <FildCard title="基础信息" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" @click="submitAllData">
        提交
      </el-button>
    </template>
    <el-form
      ref="formRef"
      :inline="true"
      :label-width="100"
      :rules="state.formRules"
      :model="state.formInline"
      class="demo-form-inline"
    >
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="生产计划单号:">
          <template #content>
            <div @click="showSelectProPlanOrder">
              <vxe-input
                v-model="state.formInline.production_plan_order_no"
                readonly
                placeholder="点击选择生产计划单"
              />
            </div>
          </template>
        </DescriptionsFormItem>

        <DescriptionsFormItem :required="true" label="通知日期:">
          <template #content>
            <el-form-item prop="notify_date">
              <el-date-picker
                v-model="state.formInline.notify_date"
                type="date"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="营销体系:">
          <template #content>
            <el-form-item prop="sale_system_id">
              <SelectComponents
                v-model="state.formInline.sale_system_id"
                :disabled="!!state.formInline.production_plan_order_id"
                api="GetSaleSystemDropdownListApi"
                label-field="name"
                value-field="id"
                clearable
                @change-value="selectSaleSystemId"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem :required="true" label="织厂名称:">
          <template #content>
            <el-form-item prop="weaving_mill_id">
              <SelectDialog
                v-model="state.formInline.weaving_mill_id"
                api="BusinessUnitSupplierEnumlist"
                :query="{
                  sale_system_id: state.formInline.sale_system_id,
                  unit_type_id: BusinessUnitIdEnum.knittingFactory,
                  name: componentRemoteSearch.name,
                }"
                :label-name="state.formInline.weaving_mill_name"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @on-input="(val) => (componentRemoteSearch.name = val)"
                @change-value="handleChangeValue"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织厂跟单QC员:">
          <template #content>
            <el-form-item prop="order_qc_user_id">
              <!--              <SelectComponents :query="{ duty: EmployeeType.followerQC }" api="GetEmployeeListEnum" label-field="name" value-field="id" v-model="state.formInline.order_qc_user_id" clearable /> -->
              <SelectDialog
                v-model="state.formInline.order_qc_user_id"
                modal_name="跟单QC员"
                :query="{
                  duty: EmployeeType.followerQC,
                  name: componentRemoteSearch.orderQcUserId,
                }"
                api="GetEmployeeListEnum"
                label-field="name"
                value-field="id"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @on-input="(val) => (componentRemoteSearch.orderQcUserId = val)"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织厂跟单:">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="state.formInline.weaving_mill_order_follower_id"
                watch-status
                default-status
                :query="{ duty: EmployeeType.follower }"
                api="GetEmployeeListEnum"
                label-field="name"
                value-field="id"
                clearable
                @select="changeFollowerId"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="跟单电话:">
          <template #content>
            <el-form-item prop="weaving_mill_order_follower_phone">
              <el-input
                v-model="state.formInline.weaving_mill_order_follower_phone"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem required label="客户名称:">
          <template #content>
            <SelectDialog
              ref="customerRef"
              v-model="state.formInline.customer_id"
              modal_name="请选择客户数据"
              :query="{
                sale_system_id: state.formInline.sale_system_id,
                name: componentRemoteSearch.customer_name,
              }"
              :label-name="state.formInline.customer_name"
              api="GetCustomerEnumList"
              :column-list="[
                {
                  title: '客户编号',
                  minWidth: 100,
                  required: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '客户编号',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '客户名称',
                  minWidth: 100,
                  colGroupHeader: true,
                  required: true,
                  childrenList: [
                    {
                      isEdit: true,
                      field: 'name',
                      title: '客户名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '电话',
                  colGroupHeader: true,
                  minWidth: 100,
                  childrenList: [
                    {
                      field: 'phone',
                      isEdit: true,
                      title: '电话',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  title: '销售员',
                  minWidth: 100,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'seller_name',
                      title: '销售员',
                      soltName: 'seller_name',
                      isEdit: true,
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @on-input="(val) => (componentRemoteSearch.customer_name = val)"
              @change-value="changeCustomer"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="跟单员:">
          <template #content>
            <SelectDialog
              ref="followerRef"
              v-model="state.formInline.customer_order_follower_id"
              modal_name="跟单员"
              :query="{
                duty: EmployeeType.follower,
                name: componentRemoteSearch.follower_name,
              }"
              api="Adminemployeelist"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @on-input="(val) => (componentRemoteSearch.follower_name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <!-- <DescriptionsFormItem :required="true" label="客户名称:">
          <template v-slot:content>
            <el-form-item prop="customer_id">
              <SelectComponents
                :query="{
                  sale_system_id: state.formInline.sale_system_id,
                }"
                watchStatus
                defaultStatus
                :queryIsData="true"
                @change-value="changeCustomer"
                :disabled="!state.formInline.production_plan_order_id && state.saleInfo"
                api="GetCustomerEnumList"
                label-field="name"
                value-field="id"
                v-model="state.formInline.customer_id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem> -->
        <DescriptionsFormItem label="销售员:" required>
          <template #content>
            <el-form-item prop="sale_user_id">
              <SelectDialog
                v-model="state.formInline.sale_user_id"
                modal_name="销售员"
                :query="{
                  duty: EmployeeType.salesman,
                  name: componentRemoteSearch.saleUserName,
                }"
                api="GetEmployeeListEnum"
                label-field="name"
                value-field="id"
                :column-list="[
                  {
                    field: 'name',
                    title: '名称',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'name',
                        isEdit: true,
                        title: '名称',
                        minWidth: 100,
                      },
                    ],
                  },
                  {
                    field: 'code',
                    title: '编号',
                    minWidth: 100,
                    isEdit: true,
                    colGroupHeader: true,
                    childrenList: [
                      {
                        field: 'code',
                        isEdit: true,
                        title: '编号',
                        minWidth: 100,
                      },
                    ],
                  },
                ]"
                @on-input="(val) => (componentRemoteSearch.saleUserName = val)"
              />
              <!--              <SelectComponents :query="{ duty: EmployeeType.salesman }" api="GetEmployeeListEnum" label-field="name" value-field="id" v-model="state.formInline.sale_user_id" clearable /> -->
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="付款期限:">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="state.formInline.payment_term"
                api="GetInfoProductPaymentTermList"
                label-field="name"
                value-field="id"
                clearable
                @select="setPaymentTermName"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="交坯日期:">
          <template #content>
            <el-form-item>
              <el-date-picker
                v-model="state.formInline.receipt_grey_fabric_date"
                :disabled-date="disabledDate"
                type="date"
                placeholder="交坯日期"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="结算方式:">
          <template #content>
            <el-form-item prop="settle_type_id">
              <SelectSettleTypeDialog
                v-model="state.formInline.settle_type_id"
                field="name"
              />
              <!--              <SelectComponents -->
              <!--                v-model="state.formInline.settle_type_id" -->
              <!--                api="GetInfoSaleSettlementMethodEnumList" -->
              <!--                label-field="name" -->
              <!--                value-field="id" -->
              <!--                clearable -->
              <!--              /> -->
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="收坯地址:" :copies="2">
          <template #content>
            <el-form-item>
              <el-input
                v-model="state.formInline.receipt_grey_fabric_address"
                style="width: 100%"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="发票抬头:">
          <template #content>
            <el-form-item>
              <SelectComponents
                v-model="state.formInline.invoice_header"
                api="GetInfoPurchaseInvoiceHeaderListUseByOther"
                label-field="name"
                value-field="id"
                clearable
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户PO号:">
          <template #content>
            <el-form-item>
              <el-input
                v-model="state.formInline.customer_po_num"
                style="width: 100%"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="备注:" :copies="2">
          <template #content>
            <el-form-item>
              <vxe-textarea
                v-model="state.formInline.order_remark"
                :autosize="{ minRows: 2, maxRows: 5 }"
              />
            </el-form-item>
          </template>
        </DescriptionsFormItem>
      </div>
    </el-form>
  </FildCard>
  <SelectProPlanOrder
    ref="SelectProPlanOrderRef"
    @handle-sure="changeProPlanOrder"
  />
</template>

<style lang="scss" scoped>
.flex_end {
  display: flex;
  justify-content: flex-end;
  // margin-bottom: 10px;
  background: #ffffff;
}
</style>
