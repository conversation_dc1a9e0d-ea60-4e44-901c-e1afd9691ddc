 <!--
  使用注意：列表页使用记得加key，否则出现数据错乱
  -->
<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { PrintDataType, PrintType as PrintTypeList } from './types'
import type { PrintBtnProps } from '@/components/PrintBtn/index.vue'
import PrintBtn from '@/components/PrintBtn/index.vue'
import { GetPrintTemplateList } from '@/api/print'
import type printApi from '@/api/printInit'
import { getDataHandler } from '@/components/PrintBtn/formatData'

defineOptions({
  name: 'PrintPopoverBtn',
})
const props = withDefaults(defineProps<PropsType>(), {
  printBtnText: '打印',
  printBtnType: 'default',
  disabled: false,
})
const emit = defineEmits(['onPrint'])

interface PropsType {
  printBtnText?: string
  printBtnType?: string
  printType?: PrintTypeList
  options?: PrintBtnProps[]
  order_type?: number
  dataType?: PrintDataType
  api?: keyof typeof printApi
  list?: any[]
  id?: number
  query?: Record<string, any>
  printBtnTextStyle?: any
  disabled?: boolean
}

const printBtnList = ref<PropsType['options']>([])

const printTemplateLength = computed(() => {
  return props.options && props.options.length > 0
    ? props.options.length
    : (printBtnList.value ? printBtnList.value.length : 0) // 提供默认值
})
// const handleBeforeEnter = () => {
//   if (!props.printType && props.options.length > 0) {
//     printBtnList.value = props.options.map((item) => {
//       item.tempId = item.id ?? props.id;
//       item.tempOrderType = item.order_type ?? props.order_type;
//       item.tempQuery = item.query ?? props.query;
//       item.tempApi = item.api ?? props.api;
//       item.tempList = item.list ?? props.list;
//       return item;
//     });
//   }
// };
watch(
  () => props.options,
  () => {
    if (props.options && props.options.length > 0) {
      printBtnList.value = props.options.map(item => ({
        ...item,
        tempId: item.id ?? props.id,
        tempOrderType: item.order_type ?? props.order_type,
        tempQuery: item.query ?? props.query,
        tempApi: item.api ?? props.api,
        tempList: item.list ?? props.list,
      }))
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

// 监听更新参数变化
watch(() => props.query, () => {
  printBtnList.value?.forEach((item: any) => {
    item.tempQuery = item.query ?? props.query
  })
})

const {
  fetchData: getPrintTemplateListApi,
  data,
  msg,
  success,
} = GetPrintTemplateList()

async function getData() {
  // 传入按钮则不用再次请求
  if (props.options?.length)
    return

  await getPrintTemplateListApi({
    type: props.printType,
  })
  if (success.value) {
    printBtnList.value = (data.value?.list || [])
      .filter((item: any) => item.status === 1 && item.data_type === props.dataType)
      .map((item: any) => {
        return {
          btnText: item.order_name,
          tempApi: props.api,
          tid: item.id,
          type:
                getDataHandler(props.printType)?.find(
                  it => it.id === item.front_handler,
                )?.code || null,
          tempOrderType: props.order_type,
          tempList: props.list,
          tempId: props.id,
          tempQuery: props.query,
        }
      })
  }
  else {
    return ElMessage.error(msg.value)
  }
}

watch(
  () => props.list,
  (newList) => {
    printBtnList.value.forEach((item: any) => {
      item.tempList = newList
    })
  },
  {
    deep: true,
  },
)

function handlePrint(row: any) {
  emit('onPrint', row)
}

onMounted(() => {
  if (props.printType)
    getData()
})
</script>

<template>
  <template v-if="printTemplateLength > 1">
    <el-popover
      placement="left"
      title="选择打印"
      trigger="hover"
      class="w-full"
      :popper-style="{ width: 'unset' }"
    >
      <template #reference>
        <el-button
          :type="props.printBtnType === 'text' ? 'primary' : 'default'" :text="props.printBtnType === 'text'"
          :link="props.printBtnType === 'text'"
          :style="props.printBtnTextStyle"
        >
          {{ printBtnText }}
        </el-button>
      </template>
      <div class="flex flex-col w-full">
        <PrintBtn
          v-for="(printBtn, index) in printBtnList"
          :id="printBtn.tempId"
          :key="index"
          :disabled="props.disabled"
          :type="printBtn.type"
          :btn-text="printBtn.btnText"
          class="w-full !ml-0"
          :tid="printBtn.tid"
          :order_type="printBtn.tempOrderType"
          :api="printBtn.tempApi"
          :list="printBtn.tempList"
          :query="printBtn.tempQuery"
          @on-print="handlePrint(printBtn)"
        />
      </div>
    </el-popover>
  </template>
  <template v-else>
    <PrintBtn
      :id="printBtnList?.[0]?.tempId"
      :btn-text="printBtnText ?? printBtnList?.[0]?.btnText"
      :btn-type="props.printBtnType"
      :type="printBtnList?.[0]?.type"
      class="w-full"
      :tid="printBtnList?.[0]?.tid"
      :order_type="printBtnList?.[0]?.tempOrderType"
      :api="printBtnList?.[0]?.tempApi"
      :disabled="props.disabled"
      :list="printBtnList?.[0]?.tempList"
      :query="printBtnList?.[0]?.tempQuery"
      plain
      @on-print="handlePrint(printBtnList?.[0])"
    />
  </template>
</template>

<style scoped></style>
