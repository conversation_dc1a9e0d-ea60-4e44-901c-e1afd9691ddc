<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { reactive, ref, watch } from 'vue'
import AddFabricDialog from '../../components/AddFabricDialog.vue'
import ProductionPlanDialog from '../../components/ProductionPlanDialog.vue'
import RelevanceSalesPlanOrder from '../../components/RelevanceSalesPlanOrder.vue'
import { DictionaryType } from '@/common/enum'
import BulkSetting from '@/components/BulkSetting/bulkModal.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import SelectRawMaterial from '@/components/SelectRawMaterial/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'
import { formatWeightDiv } from '@/common/format'
import { getDefaultCustomer } from '@/common/default'

export interface Props {
  detailLoading: boolean
  isSync?: boolean
  sale_system_id: number
}

const props = withDefaults(defineProps<Props>(), {
  detailLoading: false,
  isSync: false,
  sale_system_id: 0,
})

const emits = defineEmits(['setGreyId', 'setSchedulingWeight', 'closeLoading', 'setForm', 'clearRawList'])

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  product_code: '',
  product_name: '',
  color_name: '',
})

const state = reactive<any>({
  multipleSelection: [],
  greyData: [],
  showAdd: false,
  proData: [],
  type: '',
  production_order_no: '',
  saleSystemId: 0,
  customer_name: '',
  customer_id: 0,
  scheduling_roll: 10,
})

function setProductionOrderNo(order_no: any) {
  state.production_order_no = order_no
  if (!order_no)
    state.saleSystemId = ''
}
// 坯布信息表格
const greyTableConfig = ref({
  showSlotNums: false,
  showOperate: true,
  operateWidth: '80',
  footerMethod: (val: any) => FooterMethod(val),
  showCheckBox: true,
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  fieldApiKey: 'ProductionNoticeAdd_A',
})
// 全选中
function handAllSelect({ records }: any) {
  state.multipleSelection = records
}
// 选中
function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}
function FooterMethod({ columns, data }: any) {
  const footerData = [
    columns.map((column: any, columnIndex: number) => {
      if (columnIndex === 1)
        return '汇总'

      if (['plan_roll'].includes(column.field))
        return sumNum(data, 'plan_roll', '匹')

      if (['scheduling_roll'].includes(column.field))
        return sumNum(data, 'scheduling_roll', '匹')

      if (['scheduling_weight', 'other_weight'].includes(column.field))
        return sumNum(data, column.field, '')

      return null
    }),
  ]
  return footerData
}
const greyColumnList = ref([
  {
    field: 'sale_plan_order_no',
    title: '销售计划单号',
    soltName: 'sale_plan_order_no',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    field: 'customer_id',
    title: '所属客户',
    soltName: 'customer_id',
    minWidth: 140,
    required: true,
  },
  {
    field: 'style_no',
    title: '款号',
    soltName: 'style_no',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_width',
    soltName: 'grey_fabric_width',
    title: '坯布幅宽',
    minWidth: 180,
  },
  {
    field: 'grey_fabric_gram_weight',
    soltName: 'grey_fabric_gram_weight',
    title: '坯布克重',
    minWidth: 180,
  },
  {
    field: 'finish_product_width',
    soltName: 'finish_product_width',
    title: '成品幅宽',
    minWidth: 180,
  },
  {
    field: 'finish_product_gram_weight',
    soltName: 'finish_product_gram_weight',
    title: '成品克重',
    minWidth: 180,
  },
  {
    field: 'weight_of_fabric',
    soltName: 'weight_of_fabric',
    title: '布匹定重',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_color_id',
    soltName: 'grey_fabric_color_id',
    title: '织坯颜色',
    minWidth: 140,
  },
  {
    field: 'process_price',
    soltName: 'process_price',
    title: '加工单价',
    minWidth: 140,
  },
  {
    field: 'scheduling_roll',
    soltName: 'scheduling_roll',
    title: '排产匹数',
    minWidth: 100,
  },
  {
    field: 'scheduling_weight',
    soltName: 'scheduling_weight',
    title: '排产数量',
    minWidth: 100,
  },
  {
    field: 'other_weight',
    soltName: 'other_weight',
    title: '其他数量',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_remark',
    soltName: 'grey_fabric_remark',
    title: '备注',
    minWidth: 100,
  },
])
// 坯布删除
function handGreyDell(row: any, rowIndex: number) {
  state.greyData.splice(rowIndex, 1)
  emits('clearRawList')
}
let id = 0
// 从基础信息的生产计划单来的数据
function setList(list: any[]) {
  // list = list.map((item: any) => {
  //   return {
  //     ...item,
  //     uuid: ++id,
  //     weight_of_fabric: item.weight_of_fabric,
  //     process_price: item.process_price,
  //     scheduling_weight: item.scheduling_weight,
  //     scheduling_roll: item.scheduling_roll,
  //     production_notify_grey_fabric_detail: item.detail || [],
  //   }
  // })
  // state.greyData = list
  formatGreyData(list)
}
function selectSaleSystemId(item: any) {
  state.saleSystemId = item?.id
}
function selectCustomerId(item: any) {
  if (!props.isSync)
    return
  state.customer_id = item?.id
  state.customer_name = item?.name
  state.greyData.forEach((it) => {
    it.customer_id = state.customer_id
    it.customer_name = state.customer_name
  })
}
// 更新表格底部数据
const greyTableRef = ref<any>()
watch(
  () => state.greyData,
  (val, old) => {
    let count = 0
    state.greyData.forEach((item: any) => {
      count += Number(item.scheduling_weight) || 0
    })
    emits('setSchedulingWeight', count)
    const val_grey_fabric_id = val.length ? val[0].grey_fabric_id : 0
    const old_grey_fabric_id = old.length ? old[0].grey_fabric_id : 0
    if (state.greyData.length && val_grey_fabric_id !== old_grey_fabric_id) {
      // 编辑页面首次不需要出发
      if (!props.detailLoading)
        emits('setGreyId', state.greyData.length ? state.greyData[0].grey_fabric_id : '')
      else
        emits('closeLoading')
    }
    greyTableRef.value.tableRef.updateFooter()
  },
  { deep: true },
)
// 批量操作
const bulkSetting = ref<any>({})
const bulkList = reactive<any>([
  {
    field: 'customer_id',
    field_name: 'customer_name',
    title: '所属客户',
    component: 'select',
    api: 'GetCustomerEnumList',
  },
  {
    field: 'grey_fabric_width',
    title: '坯布幅宽',
    component: 'input',
  },
  {
    field: 'grey_fabric_gram_weight',
    title: '坯布克重(g/单位)',
    component: 'input',
    type: 'text',
  },
  {
    field: 'finish_product_width',
    title: '成品幅宽',
    component: 'input',
  },
  {
    field: 'finish_product_gram_weight',
    title: '成品克重(g/单位)',
    component: 'input',
    type: 'text',
  },
  {
    field: 'weight_of_fabric',
    title: '布匹定重(kg/单位)',
    component: 'input',
    type: 'float',
  },
  {
    field: 'grey_fabric_color_id',
    field_name: 'name',
    title: '织坯颜色',
    component: 'select',
    api: 'getInfoProductGrayFabricColorList',
  },
  {
    field: 'process_price',
    title: '加工单价(元/g/单位)',
    component: 'input',
    type: 'float',
  },
  {
    field: 'scheduling_roll',
    title: '排产匹数',
    component: 'input',
    type: 'float',
  },
  {
    field: 'grey_fabric_remark',
    title: '备注',
    component: 'input',
    type: 'text',
  },
])
async function bulkSubmit({ row, value }: any) {
  const ids = state.multipleSelection.map((item: any) => item.uuid)

  state.greyData.map((item: any) => {
    if (ids.includes(item.uuid))
      item[row.field] = value[row.field]
  })
  ElMessage.success('设置成功')
  handBulkClose()
}
const bulkShow = ref(false)
function handEdit() {
  if (state.multipleSelection.length < 1)
    return ElMessage.error('请先勾选需要修改的数据')

  bulkShow.value = true
}
function handBulkClose() {
  bulkShow.value = false
}
// 计算排产数量
function computedSchedulingWeight(index: number) {
  const { weight_of_fabric, scheduling_roll } = state.greyData[index]
  state.greyData[index].scheduling_weight = Number(weight_of_fabric) * Number(scheduling_roll)
  watchScheduling_roll(index)
}
// 监听分配
function watchScheduling_roll(index: number) {
  if (state.greyData[index].production_notify_grey_fabric_detail && state.greyData[index].production_notify_grey_fabric_detail.length) {
    let this_scheduling_roll = state.greyData[index].scheduling_roll
    // 需要判断是否为系统分配
    if (state.greyData[index].is_auto) {
      state.greyData[index].production_notify_grey_fabric_detail.forEach((item: any) => {
        if (item.can_scheduling_roll - this_scheduling_roll >= 0) {
          item.this_scheduling_roll = this_scheduling_roll
          this_scheduling_roll = 0
        }
        else if (item.can_scheduling_roll - this_scheduling_roll < 0) {
          item.this_scheduling_roll = item.can_scheduling_roll
          this_scheduling_roll = this_scheduling_roll - item.can_scheduling_roll
        }
      })
    }
  }
}
//   从资料中添加--功能
function handFabricAdd() {
  if (state.greyData.length)
    return ElMessage.error('如需更换，请先删除已有的坯布信息。')

  state.showAdd = true
}
function onSubmit(list: any) {
  const arr = list.map((item: any) => {
    return {
      uuid: ++id,
      ...item,
      ...(props.isSync
        ? {
            customer_id: state.customer_id,
            customer_name: state.customer_name,
          }
        : getDefaultCustomer(item?.customer_id)),
      grey_fabric_id: item.id,
      sale_plan_order_no: '',
      grey_fabric_width: item.grey_fabric_width,
      grey_fabric_gram_weight: item.grey_fabric_gram_weight,
      finish_product_width: item.finish_product_width,
      finish_product_gram_weight: item.finish_product_gram_weight,
      finish_product_gram_weight_unit_id: item.finish_product_gram_weight_unit_id,
      finish_product_width_unit_id: item.finish_product_width_unit_id,
      grey_fabric_gram_weight_unit_id: item.grey_fabric_gram_weight_unit_id,
      grey_fabric_width_unit_id: item.grey_fabric_width_unit_id,
      weight_of_fabric: item.weight_of_fabric,
      grey_fabric_color_id: item?.gray_fabric_color_id || 0,
      process_price: item.process_price || 0,
      grey_fabric_code: item.code,
      grey_fabric_name: item.name,
      scheduling_roll: 0, // 排产匹数
      scheduling_weight: 0,
      production_notify_grey_fabric_detail: [],
    }
  })
  state.greyData = [...state.greyData, ...arr]
  state.showAdd = false
}
//   从生产计划单添加--功能
const ProductionPlanDialogRef = ref()
function handProAdd() {
  if (state.greyData.length)
    return ElMessage.error('如需更换，请先删除已有的坯布信息。')

  ProductionPlanDialogRef.value.state.showModal = true
}

// 处理从生产计划单添加的数据信息
function formatGreyData(list: any) {
  const arr = list.map((item: any) => {
    let production_notify_grey_fabric_detail
      = item.production_sale_plan_orders.map((item: any) => {
        return {
          ...item,
          can_scheduling_roll: item.can_scheduling_roll,
          planed_roll: item.planed_roll,
          produced_roll: item.produced_roll,
          roll: item.roll,
          scheduling_roll: item.scheduling_roll,
          this_scheduling_roll: item.this_scheduling_roll,
          use_stock_roll: item.use_stock_roll,
          planed_weight: item.planed_weight,
          produced_weight: item.produced_weight,
          scheduling_weight: item.scheduling_weight,
          weight: item.weight,
          sale_plan_order_item_no: item.sale_plan_order_item_no,
          sale_plan_order_item_id: item.sale_plan_order_item_id,
        }
      }) || []
    production_notify_grey_fabric_detail = production_notify_grey_fabric_detail.sort((a: any, b: any) => new Date(a.create_time).getTime() - new Date(b.create_time).getTime())
    return {
      uuid: ++id,
      ...item,
      is_auto: true,
      grey_fabric_id: item.grey_fabric_id,
      customer_id: item.customer_id,
      sale_plan_order_no: item.production_sale_plan_orders.length ? item.production_sale_plan_orders[0].sale_plan_order_no : '',
      grey_fabric_width: item.grey_fabric_width,
      grey_fabric_gram_weight: item.grey_fabric_gram_weight,
      finish_product_width: item.finish_product_width,
      finish_product_gram_weight: item.finish_product_gram_weight,
      weight_of_fabric: item.weight_of_fabric,
      grey_fabric_color_id: item?.grey_fabric_color_id || 0,
      process_price: item.process_price || 0,
      grey_fabric_code: item.grey_fabric_code,
      grey_fabric_name: item.grey_fabric_name,
      scheduling_roll: item.plan_roll, // 排产匹数
      scheduling_weight: item.weight_of_fabric * item.plan_roll,
      production_notify_grey_fabric_detail,
    }
  })

  state.greyData = arr
}
function onSubmitPro(list: any) {
  formatGreyData(list)
  emits('setForm', { customer_id: list[0].customer_id, sale_user_id: undefined })
  state.showProAdd = false
  ProductionPlanDialogRef.value.state.showModal = false
}

//   从销售计划单中添加--功能--未完成
const AddFabricDialogRef = ref()
function handSaleAdd() {
  if (state.greyData.length)
    return ElMessage.error('如需更换，请先删除已有的坯布信息。')

  AddFabricDialogRef.value.state.showModal = true
}

function handSale(list: any) {
  const arr = list.map((item: any) => {
    let production_notify_grey_fabric_detail = item.production_notify_grey_fabric_detail || []
    production_notify_grey_fabric_detail = production_notify_grey_fabric_detail.sort((a: any, b: any) => new Date(a.create_time).getTime() - new Date(b.create_time).getTime())
    return {
      uuid: ++id,
      ...item,
      ...(props.isSync
        ? {
            customer_id: state.customer_id,
            customer_name: state.customer_name,
          }
        : getDefaultCustomer(item?.customer_id)),
      grey_fabric_id: item.grey_fabric_id,
      sale_plan_order_no: item.order_no,
      grey_fabric_color_id: 0,
      sale_plan_order_id: item.sale_product_plan_order_id,
      grey_fabric_width: item.grey_fabric_width,
      grey_fabric_gram_weight: item.grey_fabric_gram_weight,
      finish_product_width: item.product_width,
      finish_product_gram_weight: item.product_gram_weight,
      weight_of_fabric: formatWeightDiv(item.weight_of_fabric),
      grey_fabric_code: item.grey_fabric_code,
      grey_fabric_name: item.grey_fabric_name,
      process_price: 0,
      // scheduling_roll: item.scheduling_roll,
      // 2021/04/22 测试要求匹数->排产匹数，数量->排产数量：https://www.tapd.cn/41417586/bugtrace/bugs/view?bug_id=1141417586001005083
      scheduling_roll: item.roll,
      scheduling_weight: item.weight,
      is_auto: true,
      production_notify_grey_fabric_detail,
    }
  })
  state.greyData = arr
  // 带出客户名称和销售员
  emits('setForm', {
    ...(props.isSync ? { customer_id: list[0].customer_id } : {}),
    sale_user_id: list[0].sale_user_id,
  })
  AddFabricDialogRef.value.state.showModal = false
}

const SaleAllocationRef = ref()

function showSaleModel(row: any) {
  // SaleAllocationRef.value.state.showModal = true
  SaleAllocationRef.value.showDialog(row)
}
// 提交排产分配
function handleSaleAllocation(list: any) {
  state.greyData[0].production_notify_grey_fabric_detail = list?.map((item: any) => {
    return {
      ...item,
      ...(props.isSync
        ? {
            customer_id: state.customer_id,
            customer_name: state.customer_name,
          }
        : getDefaultCustomer(item?.customer_id)),
    }
  }) || []
  state.greyData[0].is_auto = list?.length ? list[0].is_auto : true

  SaleAllocationRef.value.state.showModal = false
}
// 提交所有数据
function handSubmit() {
  // let valid = true
  // state.greyData.forEach((item: any) => {
  //   if (!item.customer_id) {
  //     valid = false
  //   }
  // })
  // if (!valid) ElMessage.error('请选择所属客户')
  // if (!state.greyData.length) ElMessage.error('请添加坯布信息')
  // const all_scheduling_roll = state.greyData[0].production_notify_grey_fabric_detail?.reduce((pre: any, val: any) => pre + Number(val.scheduling_roll), 0) || 0
  // if (Number(state.greyData[0].scheduling_roll) < all_scheduling_roll) {
  //   return ElMessage.error('排产匹数已超过关联的销售计划单可排产匹数之和')
  // }
  const formList = state.greyData?.map((item: any) => {
    return {
      ...item,
      uuid: undefined,
    }
  })
  // const form = formList[0]
  return formList[0]
  //
}

// 回显数据
function setData(objData: any) {
  const keys = [
    'grey_fabric_id',
    'grey_fabric_width',
    'grey_fabric_gram_weight',
    'finish_product_width',
    'finish_product_gram_weight',
    'weight_of_fabric',
    'grey_fabric_color_id',
    'process_price',
    'grey_fabric_code',
    'grey_fabric_name',
    'scheduling_roll',
    'scheduling_weight',
    'other_weight',
    'customer_id',
    'grey_fabric_remark',
    'is_auto',
    'grey_fabric_width_unit_id',
    'grey_fabric_gram_weight_unit_id',
    'finish_product_width_unit_id',
    'finish_product_gram_weight_unit_id',
    'customer_name',
    'style_no',
  ]
  let { production_notify_grey_fabric_detail = [] } = objData
  state.production_order_no = objData.production_plan_order_no
  production_notify_grey_fabric_detail
    = production_notify_grey_fabric_detail?.map((item: any) => {
      return {
        ...item,
        roll: item.roll,
        weight: item.weight,
        planed_roll: item.planed_roll,
        planed_weight: item.planed_weight,
        scheduling_roll: item.scheduling_roll,
        scheduling_weight: item.scheduling_weight,
        produced_roll: item.produced_roll,
        produced_weight: item.produced_weight,
        use_stock_roll: item.use_stock_roll,
        can_scheduling_roll: item.can_scheduling_roll,
        this_scheduling_roll: item.this_scheduling_roll,
      }
    }) || []
  production_notify_grey_fabric_detail = production_notify_grey_fabric_detail.sort((a: any, b: any) => new Date(a.create_time).getTime() - new Date(b.create_time).getTime())
  const item: any
    = {
      uuid: ++id,
      production_notify_grey_fabric_detail,
    } || []
  keys.forEach((key: string) => {
    item[key] = objData[key]
  })
  state.greyData = [
    {
      ...item,
      scheduling_roll: item.scheduling_roll,
      weight_of_fabric: item.weight_of_fabric,
      process_price: item.process_price,
      scheduling_weight: item.scheduling_weight,
    },
  ]
}

// 注入详细信息
function setLineData(obj: any) {
  // 匹配id
  const setListIdx = state.greyData.findIndex((e: any) => e.grey_fabric_id === obj.id)
  if (setListIdx !== -1) {
    if (obj.grey_fabric_width_unit_id)
      state.greyData[setListIdx].grey_fabric_width_unit_id = obj.grey_fabric_width_unit_id
    if (obj.grey_fabric_gram_weight_unit_id)
      state.greyData[setListIdx].grey_fabric_gram_weight_unit_id = obj.grey_fabric_gram_weight_unit_id
    if (obj.finish_product_width_unit_id)
      state.greyData[setListIdx].finish_product_width_unit_id = obj.finish_product_width_unit_id
    if (obj.finish_product_gram_weight_unit_id)
      state.greyData[setListIdx].finish_product_gram_weight_unit_id = obj.finish_product_gram_weight_unit_id
  }
}

defineExpose({
  setList,
  selectSaleSystemId,
  selectCustomerId,
  handSubmit,
  setData,
  setProductionOrderNo,
  setLineData,
})
</script>

<template>
  <FildCard title="坯布信息" class="mt-[5px]">
    <template #right-top>
      <el-button type="primary" :disabled="state.multipleSelection.length <= 0" @click="handEdit">
        批量操作
      </el-button>
      <el-button :disabled="state.production_order_no || state.greyData.length ? true : false" style="margin-left: 10px" type="primary" @click="handFabricAdd">
        从资料中添加
      </el-button>
      <el-button :disabled="state.production_order_no ? false : true" style="margin-left: 10px" type="primary" @click="handProAdd">
        从生产计划单中添加
      </el-button>
      <el-button :disabled="!(state.saleSystemId && !state.production_order_no)" style="margin-left: 10px" type="primary" @click="handSaleAdd">
        从销售计划单中添加
      </el-button>
    </template>
    <Table ref="greyTableRef" :config="greyTableConfig" :table-list="state.greyData" :column-list="greyColumnList">
      <template #sale_plan_order_no="{ row }">
        <el-button type="text" @click="showSaleModel(row)">
          查看
        </el-button>
      </template>
      <template #customer_id="{ row }">
        <SelectDialog
          v-model="row.customer_id"
          disabled
          :label-name="row.customer_name"
          api="GetCustomerEnumList"
          :query="{ name: componentRemoteSearch.customer_name }"
          :column-list="[
            {
              title: '客户编号',
              minWidth: 100,
              required: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'code',
                  isEdit: true,
                  title: '客户编号',
                  minWidth: 100,
                },
              ],
            },
            {
              title: '客户名称',
              minWidth: 100,
              colGroupHeader: true,
              required: true,
              childrenList: [
                {
                  isEdit: true,
                  field: 'name',
                  title: '客户名称',
                  minWidth: 100,
                },
              ],
            },
            {
              title: '电话',
              colGroupHeader: true,
              minWidth: 100,
              childrenList: [
                {
                  field: 'phone',
                  isEdit: true,
                  title: '电话',
                  minWidth: 100,
                },
              ],
            },
            {
              title: '销售员',
              minWidth: 100,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'seller_name',
                  title: '销售员',
                  soltName: 'seller_name',
                  isEdit: true,
                  minWidth: 100,
                },
              ],
            },
          ]"
          @change-input="val => (componentRemoteSearch.customer_name = val)"
        />
      </template>
      <template #style_no="{ row }">
        <vxe-input v-model="row.style_no" size="mini" maxlength="200" />
      </template>
      <template #grey_fabric_width="{ row }">
        <!-- <vxe-input size="mini" v-model="row.grey_fabric_width" maxlength="200"></vxe-input> -->
        <el-input v-model="row.grey_fabric_width" clearable class="input-with-select">
          <template #append>
            <SelectComponents
              v-model="row.grey_fabric_width_unit_id"
              placeholder="单位"
              style="width: 80px"
              :query="{ dictionary_id: DictionaryType.width_unit }"
              api="GetDictionaryDetailEnumListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </el-input>
      </template>
      <template #grey_fabric_gram_weight="{ row }">
        <!-- <vxe-input type="text" v-model="row.grey_fabric_gram_weight"></vxe-input> -->
        <el-input v-model="row.grey_fabric_gram_weight" clearable class="input-with-select">
          <template #append>
            <SelectComponents
              v-model="row.grey_fabric_gram_weight_unit_id"
              placeholder="单位"
              style="width: 80px"
              :query="{ dictionary_id: DictionaryType.gram_weight_unit }"
              api="GetDictionaryDetailEnumListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </el-input>
      </template>
      <template #finish_product_width="{ row }">
        <!-- <vxe-input size="mini" v-model="row.finish_product_width" maxlength="200"></vxe-input> -->
        <el-input v-model="row.finish_product_width" clearable class="input-with-select">
          <template #append>
            <SelectComponents
              v-model="row.finish_product_width_unit_id"
              placeholder="单位"
              style="width: 80px"
              :query="{ dictionary_id: DictionaryType.width_unit }"
              api="GetDictionaryDetailEnumListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </el-input>
      </template>
      <template #finish_product_gram_weight="{ row }">
        <!-- <vxe-input type="text" v-model="row.finish_product_gram_weight"></vxe-input> -->
        <el-input v-model="row.finish_product_gram_weight" clearable class="input-with-select">
          <template #append>
            <SelectComponents
              v-model="row.finish_product_gram_weight_unit_id"
              placeholder="单位"
              style="width: 80px"
              :query="{ dictionary_id: DictionaryType.gram_weight_unit }"
              api="GetDictionaryDetailEnumListApi"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </el-input>
      </template>
      <template #weight_of_fabric="{ row, rowIndex }">
        <vxe-input v-model="row.weight_of_fabric" type="float" @change="computedSchedulingWeight(rowIndex)">
          <template #suffix>
            {{ row.unit_name }}
          </template>
        </vxe-input>
      </template>
      <template #grey_fabric_color_id="{ row }">
        <SelectDialog
          v-model="row.grey_fabric_color_id"
          :query="{ name: componentRemoteSearch.color_name }"
          :label-name="row.grey_fabric_color_name"
          :column-list="[
            {
              field: 'name',
              title: '名称',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'name',
                  isEdit: true,
                  title: '名称',
                  minWidth: 100,
                },
              ],
            },
            {
              field: 'code',
              title: '编号',
              minWidth: 100,
              isEdit: true,
              colGroupHeader: true,
              childrenList: [
                {
                  field: 'code',
                  isEdit: true,
                  title: '编号',
                  minWidth: 100,
                },
              ],
            },
          ]"
          :table-column="[
            {
              field: 'name',
              title: '颜色',
            },
          ]"
          api="getInfoProductGrayFabricColorList"
          @change-input="val => (componentRemoteSearch.color_name = val)"
        />
        <!-- <SelectComponents size="small" ref="customerRef" v-model="row.grey_fabric_color_id" api="getInfoProductGrayFabricColorList" label-field="name" value-field="id"></SelectComponents> -->
      </template>
      <template #process_price="{ row }">
        <vxe-input v-model="row.process_price" type="float">
          <template #suffix>
            <span v-if="row.unit_name">元/{{ row.unit_name }}</span>
            <span v-else />
          </template>
        </vxe-input>
      </template>
      <template #production_plan_type="{ row }">
        <SelectComponents v-model="row.production_plan_type" size="small" api="PlanTypeDropdownList" label-field="name" value-field="id" />
      </template>
      <template #plan_roll="{ row }">
        <vxe-input v-model="row.plan_roll" type="float" size="mini" maxlength="200" />
      </template>
      <!--      排产数量 -->
      <template #scheduling_weight="{ row }">
        <vxe-input v-model="row.scheduling_weight" type="float" size="mini">
          <template #suffix>
            {{ row.unit_name }}
          </template>
        </vxe-input>
        <!--        {{ Number(row.scheduling_weight).toFixed(2) }}kg -->
      </template>
      <template #scheduling_roll="{ row, rowIndex }">
        <vxe-input v-model="row.scheduling_roll" type="float" size="mini" @change="computedSchedulingWeight(rowIndex)" />
      </template>
      <template #grey_fabric_remark="{ row }">
        <vxe-input v-model="row.grey_fabric_remark" size="mini" maxlength="200" />
      </template>
      <template #other_weight="{ row }">
        <vxe-input v-model="row.other_weight" type="float" size="mini">
          <template #suffix>
            {{ row.unit_name }}
          </template>
        </vxe-input>
      </template>
      <template #operate="{ row, rowIndex }">
        <el-button type="text" @click="handGreyDell(row, rowIndex)">
          删除
        </el-button>
      </template>
    </Table>
    <!-- <el-button type="primary" @click="handSubmit">提交</el-button> -->
  </FildCard>
  <BulkSetting v-model="bulkSetting" :column-list="bulkList" :show="bulkShow" @submit="bulkSubmit" @close="handBulkClose">
    <template #brand="{ row }">
      <el-input v-model="bulkSetting[row.field]" />
    </template>
  </BulkSetting>
  <ProductionPlanDialog ref="ProductionPlanDialogRef" :filter-data-default="{ order_no: state.production_order_no }" @handle-sure="onSubmitPro" />
  <!-- 添加坯布信息商品 -->
  <SelectRawMaterial v-model="state.showAdd" :multiple="false" hand-binary @submit="onSubmit" />
  <AddFabricDialog ref="AddFabricDialogRef" :sale_system_id="state.saleSystemId" :multiple="false" @handle-sure="handSale" />
  <RelevanceSalesPlanOrder ref="SaleAllocationRef" type="add" @handle-sure="handleSaleAllocation" />
</template>
