<script setup lang="ts">
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import PrintRecordDialog from './components/PrintRecordDialog.vue'
import { BusinessUnitIdEnum } from '@/common/enum'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import { GetFineCodeList, GetProductionScheduleOrderList, PrintFineCode } from '@/api/scheduleProduction'
import { usePrintTemplate } from '@/components/PrintPopoverBtn/index'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import { debounce, getFilterData } from '@/common/util'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import MachineSchedulingDialog from '@/pages/productionManagement/productionNotice/components/MachineSchedulingDialog.vue'
import { formatDate } from '@/common/format'

const { options } = usePrintTemplate({
  printType: PrintType.PrintTemplateTypeStock,
  dataType: PrintDataType.Grey,
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  unit_name: '',
  raw_name: '',
  raw_code: '',
})
const state = reactive<any>({
  tableData: [],
  filterData: {
    order_no: '', // 生产排产单号
    machine_number: '', // 机台号
    material_batch_numbers: '', // 用料批号
    weaving_mill_id: '', // 织厂ID (biz_unit_id)
    customer_id: '', // 客户ID
    print_status: '', // 布飞状态
    schedule_date_start: '', // 排产日期开始
    schedule_date_end: '', // 排产日期结束
    sale_system_id: '', // 营销体系ID
    devierDate: [], // 排产日期范围
  },
  multipleSelection: [],
  isShow: false, // 是否显示未打印条码
  currentSelectedRow: null, // 当前选中的行数据
  // 打印记录弹窗相关
  showPrintRecordDialog: false,
  selectedFineCodeId: 0,
  selectedFineCodeName: '',
})
const {
  fetchData: ApiCustomerList,
  data: datalist,
  total,
  loading,
  page,
  size,
  success,
  msg,
  handleSizeChange,
  handleCurrentChange,
} = GetProductionScheduleOrderList()
// 首次加载数据
onMounted(() => {
  getData()
})

onActivated(() => {
  getData()
})
// 生产排产单表格列配置
const columnList = ref([
  {
    sortable: true,
    field: 'weave_factory_name',
    title: '织厂名称',
    fixed: 'left' as const,
    width: 120,
  },
  {
    sortable: true,
    field: 'production_notify_order_no',
    title: '生产通知单',
    fixed: 'left' as const,
    soltName: 'production_notify_order_no',
    width: 140,
  },
  {
    sortable: true,
    field: 'order_no',
    title: '排产单号',
    fixed: 'left' as const,
    minWidth: 140,
  },
  {
    sortable: true,
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'yarn_batch',
    title: '纱批',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'machine_number',
    title: '机台',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'contract_sequence',
    title: '合同号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'remark',
    title: '合同备注',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'grey_fabric_color_name',
    title: '坯布颜色',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'fabric_finishing',
    title: '布种后整',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'yarn_brand',
    title: '原料品牌',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'yarn_name',
    title: '原料纱名',
    minWidth: 150,
  },
  {
    sortable: true,
    field: 'in_warehouse_weight',
    title: '进仓数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'in_warehouse_roll',
    title: '进仓条数',
    isPrice: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'weighing_weight',
    title: '称重数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    sortable: true,
    field: 'weighing_roll',
    isPrice: true,
    title: '称重条数',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'inspection_weight',
    title: '验布数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'inspection_roll',
    title: '验布条数',
    isPrice: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'out_warehouse_weight',
    title: '出仓数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'out_warehouse_roll',
    title: '出仓条数',
    isPrice: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'stock_weight',
    title: '库存数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'stock_roll',
    title: '库存条数',
    isPrice: true,
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'schedule_date',
    title: '排产日期',
    minWidth: 120,
    isDate: true,
  },
  {
    sortable: true,
    field: 'schedule_roll',
    title: '排产条数',
    isPrice: true,
    minWidth: 100,
  },
  {
    field: 'create_time',
    title: '创建时间',
    isDate: true,
    minWidth: 150,
  },
  {
    field: 'creator_name',
    title: '创建人',
    minWidth: 100,
  },
  {
    field: 'audit_status_name',
    soltName: 'audit_status_name',
    title: '审核状态',
    fixed: 'right' as const,
    minWidth: 100,
  },
])
const columnList1 = ref([
  {
    field: 'machine_number',
    title: '机台',
    minWidth: 100,
  },
  {
    field: 'fabric_piece_code',
    title: '条形码',
    minWidth: 150,
  },
  {
    field: 'volume_number',
    title: '卷号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_level_name',
    title: '坯布等级',
    minWidth: 100,
  },
  {
    field: 'quality_remark',
    title: '质量备注',
    minWidth: 150,
  },
  {
    field: 'in_warehouse_weight',
    title: '进仓数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    field: 'in_warehouse_roll',
    title: '进仓条数',
    isPrice: true,
    minWidth: 100,
  },
  {
    field: 'weighing_weight',
    title: '称重数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    field: 'weighing_roll',
    title: '称重条数',
    isPrice: true,
    minWidth: 100,
  },
  {
    field: 'inspection_weight',
    title: '验布数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    field: 'inspection_roll',
    title: '验布条数',
    isPrice: true,
    minWidth: 100,
  },
  {
    field: 'out_warehouse_weight',
    title: '出仓数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    field: 'out_warehouse_roll',
    title: '出仓条数',
    isPrice: true,
    minWidth: 100,
  },
  {
    field: 'stock_weight',
    title: '库存数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    field: 'stock_roll',
    title: '库存条数',
    isPrice: true,
    minWidth: 100,
  },
  {
    field: 'printer_name',
    title: '打印人',
    minWidth: 100,
  },
  {
    field: 'print_time',
    title: '打印时间',
    isDate: true,
    minWidth: 150,
  },
  {
    field: 'print_status',
    soltName: 'print_status',
    title: '打印状态',
    minWidth: 100,
  },
])
// 获取数据
async function getData() {
  // 处理日期范围
  const filterData = { ...state.filterData }
  if (filterData.devierDate && filterData.devierDate.length === 2) {
    filterData.schedule_date_start = formatDate(filterData.devierDate[0])
    filterData.schedule_date_end = formatDate(filterData.devierDate[1])
  }
  delete filterData.devierDate

  // 处理织厂ID映射
  if (filterData.weaving_mill_id) {
    filterData.biz_unit_id = filterData.weaving_mill_id
    delete filterData.weaving_mill_id
  }
  await ApiCustomerList(getFilterData(filterData))
  if (!success.value)
    return ElMessage.error(msg.value)

  handleDblClick({ row: datalist.value?.list?.[0] })
}
// 实时过滤数据
watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  {
    deep: true,
  },
)

// 监听"显示未打印条码"复选框变化
watch(
  () => state.isShow,
  () => {
    // 当复选框状态改变时，重新获取细码数据
    refreshFineCodeData()
  },
)
// 生产通知单表格列配置
const tableConfig = ref({
  fieldApiKey: 'ScheduleProduction',
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  cellDBLClickEvent: handleDblClick,
  showCheckBox: true,
  showOperate: true,
  height: '100%',
  operateWidth: '140',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
})
const { fetchData: fetchFineCodeData, data: fineCodeData, loading: fineCodeLoading } = GetFineCodeList()

// 双击行获取细码列表
async function handleDblClick({ row }: { row: any }) {
  try {
    // 保存当前选中的行数据
    state.currentSelectedRow = row

    // 构建请求参数
    const params: any = {
      production_schedule_order_id: row.id,
    }

    // 如果勾选了"显示未打印条码"，则只获取未打印的数据
    if (state.isShow)
      params.print_status = 1 // 1表示未打印

    // 根据生产排产单ID获取细码列表
    await fetchFineCodeData(params)

    // 更新下方表格数据
    state.tableData = fineCodeData.value?.list || []
  }
  catch (error) {
    console.error('获取细码列表失败:', error)
  }
}

// 重新获取细码数据的函数
async function refreshFineCodeData() {
  if (state.currentSelectedRow) {
    await handleDblClick({ row: state.currentSelectedRow })
  }
  else if (state.isShow) {
    // 如果没有选中行但勾选了显示未打印条码，给用户提示
    ElMessage.info('请先双击上方表格行选择排产单，然后再筛选未打印条码')
  }
}
// 细码列表表格配置
const tableConfig1 = ref({
  loading: fineCodeLoading,
  showPagition: false,
  showSlotNums: true,
  showCheckBox: true,
  showOperate: true,
  height: '100%',
  operateWidth: '150', // 增加操作列宽度以容纳两个按钮
  showSort: false,
  checkboxConfig: {
    checkField: 'selected',
    highlight: true,
    reserve: true,
  },
  handleAllSelect,
  handleSelectionChange,
})
// 细码表格的 ref
const fineCodeTableRef = ref()
function handleAllSelect() {
  state.multipleSelection = fineCodeTableRef.value?.tableRef?.getCheckboxRecords() || []
}
function handleSelectionChange() {
  state.multipleSelection = fineCodeTableRef.value?.tableRef?.getCheckboxRecords() || []
}

// 打开打印记录弹窗
function openPrintRecordDialog(row: any) {
  state.selectedFineCodeId = row.id
  state.selectedFineCodeName = row.fabric_piece_code || `细码-${row.id}`
  state.showPrintRecordDialog = true
}

// 机台排产编辑相关
const showEditDialog = ref(false)
const editRowData = ref<any>({})
const mode = ref('edit')
function handleEdit(row: Api.GetProductionScheduleOrderList.Response) {
  mode.value = 'edit'
  editRowData.value = { ...row }
  showEditDialog.value = true
}

// 处理编辑保存
function handleEditSave() {
  // 这里可以调用API保存数据
  showEditDialog.value = false
  // 重新获取数据
  getData()
}
function handleClickAdd() {
  mode.value = 'add'
  editRowData.value = { }
  showEditDialog.value = true
}
const router = useRouter()

function jumpNotifyDetail(id: number) {
  router.push({
    name: 'ProductionNoticeDetail',
    query: {
      id,
    },
  })
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="排产日期:">
          <template #content>
            <SelectDate v-model="state.filterData.devierDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="排产单号:">
          <template #content>
            <el-input v-model="state.filterData.order_no" clearable placeholder="排产单号" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销部门:">
          <template #content>
            <SelectComponents v-model="state.filterData.sale_system_id" label-field="name" value-field="id" api="GetSaleSystemDropdownListApi" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织厂名称:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.weaving_mill_id"
              api="business_unitlist"
              :query="{
                unit_type_id: BusinessUnitIdEnum.knittingFactory,
                name: componentRemoteSearch.name,
              }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="(val) => (componentRemoteSearch.name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称:">
          <template #content>
            <SelectCustomerDialog v-model="state.filterData.customer_id" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="机台号:">
          <template #content>
            <el-input v-model="state.filterData.machine_number" clearable placeholder="机台号" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="用料批号:">
          <template #content>
            <el-input v-model="state.filterData.material_batch_numbers" clearable placeholder="用料批号" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="布飞状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.print_status"
              api="EnumPrintStatus"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full" :tool-bar="false">
      <template #right-top>
        <el-button type="primary" @click="handleClickAdd">
          新增生产排产单
        </el-button>
      </template>
      <Table
        :config="tableConfig"
        :table-list="datalist.list"
        :column-list="columnList"
      >
        <template #production_notify_order_no="{ row }">
          <el-link type="primary" :underline="false" @click="jumpNotifyDetail(row.production_notify_order_id)">
            {{ row.production_notify_order_no }}
          </el-link>
        </template>
        <template #audit_status_name="{ row }">
          <div class="flex items-center">
            <div
              :class="{
                yuan: row.audit_status === 2,
                yuan_red: row.audit_status === 3 || row.audit_status === 4,
                yuan_yellow: row.audit_status === 1,
              }"
            />
            <div
              :class="{
                yuan_font: row.audit_status === 2,
                yuan_font_active: row.audit_status === 3 || row.audit_status === 4,
                yuan_font_warning: row.audit_status === 1,
              }"
            >
              {{ row.audit_status_name || '未知状态' }}
            </div>
          </div>
        </template>
        <template #print_status="{ row }">
          <div class="flex items-center">
            <div :class="row.status === 1 ? 'yuan' : 'yuan_red'" />
            <div :class="row.status === 1 ? 'yuan_font' : 'yuan_font_active'">
              {{ row?.status === 1 ? "启用" : "禁用" }}
            </div>
          </div>
        </template>
        <template #operate="{ row }">
          <PrintPopoverBtn
            :key="row.id"
            style="width: auto"
            print-btn-text="打印"
            print-btn-type="text"
            :query="{
              production_schedule_order_id: row.id,
              id: undefined,
              // ids: row.id,
            }"
            api="PrintFineCode"
            :options="options"
          />
          <el-link type="primary" class="ml-2" :underline="false" @click="handleEdit(row)">
            编辑
          </el-link>
        </template>
      </Table>
    </FildCard>
    <FildCard title="细码列表" class="table-card-bottom" :tool-bar="false">
      <div class="mb-2">
        <PrintPopoverBtn
          style="width: auto"
          print-btn-text="打印布飞"
          print-btn-type="button"
          :disabled="!state.multipleSelection.length"
          :query="{
            production_schedule_order_id: state.tableData?.[0]?.production_schedule_order_id,
            ids: state.multipleSelection?.map(item => item.id)?.join(','),
          }"
          api="PrintFineCode"
          :options="options"
        />
        <el-checkbox v-model="state.isShow" class="ml-2">
          仅显示未打印条码
        </el-checkbox>
        <span class="ml-4 text-gray-500">
          提示：双击上方表格行可查看对应的细码列表
        </span>
      </div>
      <Table
        ref="fineCodeTableRef"
        :config="tableConfig1"
        :table-list="state.tableData"
        :column-list="columnList1"
      >
        <template #print_status="{ row }">
          <div class="flex items-center">
            <div :class="row.print_status === 1 ? 'yuan_red' : 'yuan'" />
            <div :class="row.print_status === 1 ? 'yuan_font_active' : 'yuan_font'">
              {{ row.print_status === 1 ? "未打印" : "已打印" }}
            </div>
          </div>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <PrintPopoverBtn
              :id="row.id"
              :key="row.id"
              style="width: auto"
              :print-btn-text="row.print_status === 3 ? '已打印' : '打印'"
              print-btn-type="text"
              :query="{
                production_schedule_order_id: row.production_schedule_order_id,
                ids: `${row.id}`,
              }"
              api="PrintFineCode"
              :options="options"
            />
            <!-- <el-link
              type="primary"
              :underline="false"
              :disabled="row.print_status === 2"
              @click="handlePrintSingle(row)"
            >
              {{ row.print_status === 2 ? '已打印' : '打印' }}
            </el-link> -->
            <el-link
              type="success"
              :underline="false"
              @click="openPrintRecordDialog(row)"
            >
              打印记录
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>

    <!-- 机台排产编辑弹窗 -->
    <MachineSchedulingDialog
      v-model="showEditDialog"
      :mode="mode"
      :production-data="editRowData"
      @save="handleEditSave"
    />

    <!-- 打印记录弹窗 -->
    <PrintRecordDialog
      v-model="state.showPrintRecordDialog"
      :fine-code-id="state.selectedFineCodeId"
      :fine-code-name="state.selectedFineCodeName"
    />
  </div>
</template>

<style scoped>
/* 审核状态指示器样式 */
.yuan {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #52c41a;
  margin-right: 8px;
}

.yuan_red {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ff4d4f;
  margin-right: 8px;
}

.yuan_yellow {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #faad14;
  margin-right: 8px;
}

/* 审核状态文字样式 */
.yuan_font {
  color: #52c41a;
  font-weight: 500;
}

.yuan_font_active {
  color: #ff4d4f;
  font-weight: 500;
}

.yuan_font_warning {
  color: #faad14;
  font-weight: 500;
}
</style>
