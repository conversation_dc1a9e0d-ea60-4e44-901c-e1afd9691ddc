<script setup lang="ts">
import { computed, ref, watch } from 'vue'

// 定义组件的 Props 类型
interface DigitalKeyboardProps {
  /** 当前编辑步骤 */
  editStep?: number
  /** 步骤标题映射 */
  stepTitles?: Record<number, string>
  /** 步骤描述映射 */
  stepDescriptions?: Record<number, string>
  /** 是否显示小数点按钮 */
  showDot?: boolean
  /** 是否显示清除按钮 */
  showClear?: boolean
  /** 是否显示退格按钮 */
  showBackspace?: boolean
  /** 是否显示确认按钮 */
  showConfirm?: boolean
  /** 是否显示取消按钮 */
  showCancel?: boolean
  /** 是否显示步骤导航 */
  showStepNavigation?: boolean
  /** 总步骤数 */
  totalSteps?: number
  /** 禁用的数字（针对特定步骤） */
  disabledNumbers?: Record<number, number[]>
  /** 当前选中的值（用于高亮显示） */
  selectedValue?: number | string
  /** 键盘布局模式 */
  layout?: 'standard' | 'compact' | 'minimal'
  /** 是否显示进度指示器 */
  showProgress?: boolean
}

// 定义 Props
const props = withDefaults(defineProps<DigitalKeyboardProps>(), {
  editStep: 1,
  stepTitles: () => ({
    1: '疵点位置',
    2: '疵点数量',
    3: '疵点分数',
  }),
  stepDescriptions: () => ({
    1: '请输入位置',
    2: '请输入个数',
    3: '请选择分数',
  }),
  showDot: true,
  showClear: true,
  showBackspace: true,
  showConfirm: false,
  showCancel: false,
  showStepNavigation: true,
  totalSteps: 3,
  disabledNumbers: () => ({}),
  selectedValue: undefined,
  layout: 'standard',
  showProgress: true,
})

// 定义 Emits
const emits = defineEmits<{
  'number-input': [number: number]
  'dot-input': []
  'clear': []
  'backspace': []
  'confirm': []
  'cancel': []
  'step-change': [step: number]
}>()

// 计算属性
const currentStepTitle = computed(() => {
  return props.stepTitles[props.editStep] || `步骤 ${props.editStep}`
})

const currentStepDescription = computed(() => {
  return props.stepDescriptions[props.editStep] || ''
})

const stepProgress = computed(() => {
  return `${props.editStep}/${props.totalSteps}`
})

const currentDisabledNumbers = computed(() => {
  return props.disabledNumbers[props.editStep] || []
})

// 数字按钮处理
function handleNumberClick(num: number) {
  if (currentDisabledNumbers.value.includes(num))
    return
  emits('number-input', num)
}

// 小数点按钮处理
function handleDotClick() {
  emits('dot-input')
}

// 清除按钮处理
function handleClearClick() {
  emits('clear')
}

// 退格按钮处理
function handleBackspaceClick() {
  emits('backspace')
}

// 确认按钮处理
function handleConfirmClick() {
  emits('confirm')
}

// 取消按钮处理
function handleCancelClick() {
  emits('cancel')
}

// 步骤导航
function handlePrevStep() {
  if (props.editStep > 1)
    emits('step-change', props.editStep - 1)
}

function handleNextStep() {
  if (props.editStep < props.totalSteps)
    emits('step-change', props.editStep + 1)
}

// 判断数字是否被选中
function isNumberSelected(num: number): boolean {
  return props.selectedValue === num
}

// 判断数字是否被禁用
function isNumberDisabled(num: number): boolean {
  return currentDisabledNumbers.value.includes(num)
}

// 获取按钮样式类
function getNumberButtonClass(num: number): string {
  const baseClass = 'h-12 rounded text-4xl font-medium transition-colors'

  if (isNumberDisabled(num))
    return `${baseClass} bg-gray-100 border border-gray-200 text-gray-400 cursor-not-allowed`

  if (isNumberSelected(num))
    return `${baseClass} !bg-[#409eff] !border-blue-500 !text-white selected`

  return `${baseClass} bg-white border border-gray-300 hover:bg-gray-50 active:bg-gray-100`
}

// 获取特殊按钮样式类
function getSpecialButtonClass(disabled = false): string {
  const baseClass = 'h-12 rounded text-lg font-medium transition-colors'

  if (disabled)
    return `${baseClass} bg-gray-100 border border-gray-200 text-gray-400 cursor-not-allowed`

  return `${baseClass} bg-white border border-gray-300 hover:bg-gray-50 active:bg-gray-100`
}

// 布局相关计算
const keyboardClass = computed(() => {
  const baseClass = 'digital-keyboard keyboard-container'

  switch (props.layout) {
    case 'compact':
      return `${baseClass} compact-layout`
    case 'minimal':
      return `${baseClass} minimal-layout`
    default:
      return baseClass
  }
})
</script>

<template>
  <div :class="keyboardClass">
    <div class="space-y-4">
      <!-- 步骤标题和进度 -->
      <div v-if="showProgress" class="text-center">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-lg font-medium text-gray-800">
            {{ currentStepTitle }}
          </h3>
          <span class="text-sm text-gray-400 bg-gray-100 px-2 py-1 rounded">
            {{ stepProgress }}
          </span>
        </div>
        <p class="text-sm text-gray-500">
          {{ currentStepDescription }}
        </p>
      </div>

      <!-- 步骤导航按钮 -->
      <div v-if="showStepNavigation && totalSteps > 1" class="flex justify-between space-x-2">
        <el-button
          :disabled="editStep <= 1"
          size="small"
          @click="handlePrevStep"
        >
          上一步
        </el-button>
        <el-button
          :disabled="editStep >= totalSteps"
          size="small"
          @click="handleNextStep"
        >
          下一步
        </el-button>
      </div>

      <!-- 数字键盘 -->
      <div class="grid grid-cols-3 gap-2">
        <!-- 数字 1-9 -->
        <button
          v-for="num in [1, 2, 3, 4, 5, 6, 7, 8, 9]"
          :key="num"
          :disabled="isNumberDisabled(num)"
          :class="getNumberButtonClass(num)"
          @click="handleNumberClick(num)"
        >
          {{ num }}
        </button>

        <!-- 第四行：小数点、0、退格 -->
        <button
          v-if="showDot"
          :disabled="[3, 2].includes(editStep)"
          :class="getSpecialButtonClass([3, 2].includes(editStep))"
          @click="handleDotClick"
        >
          .
        </button>
        <button
          v-else
          class="h-12 rounded bg-gray-50 border border-gray-200 cursor-default"
        />

        <button
          :disabled="isNumberDisabled(0)"
          :class="getNumberButtonClass(0)"
          @click="handleNumberClick(0)"
        >
          0
        </button>

        <button
          v-if="showBackspace"
          :class="getSpecialButtonClass()"
          @click="handleBackspaceClick"
        >
          ⌫
        </button>
        <button
          v-else
          class="h-12 rounded bg-gray-50 border border-gray-200 cursor-default"
        />
      </div>

      <!-- 功能按钮区域 -->
      <div v-if="showClear || showConfirm || showCancel" class="space-y-2">
        <el-button
          v-if="showClear"
          size="large"
          class="w-full"
          @click="handleClearClick"
        >
          清除
        </el-button>

        <div v-if="showConfirm || showCancel" class="flex space-x-2">
          <el-button
            v-if="showCancel"
            size="large"
            class="flex-1"
            @click="handleCancelClick"
          >
            取消
          </el-button>
          <el-button
            v-if="showConfirm"
            type="primary"
            size="large"
            class="flex-1"
            @click="handleConfirmClick"
          >
            确认
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 数字键盘容器基础样式 */
.digital-keyboard {
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  color: white;
  padding: 16px;
  min-width: 200px;
}

.digital-keyboard h3 {
  color: white !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.digital-keyboard p {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* 数字键盘按钮美化 */
.digital-keyboard button {
  background: rgba(255, 255, 255, 0.9) !important;
  border: none !important;
  color: #667eea !important;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.digital-keyboard button:hover:not(:disabled) {
  background: white !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.digital-keyboard button:active:not(:disabled) {
  transform: translateY(0);
  animation: buttonPress 0.1s ease-in-out;
}

.digital-keyboard button:disabled {
  background: rgba(255, 255, 255, 0.3) !important;
  color: rgba(255, 255, 255, 0.5) !important;
  cursor: not-allowed;
}

/* 选中状态的数字按钮 */
.digital-keyboard button.selected {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%) !important;
  color: #8b4513 !important;
  box-shadow: 0 0 20px rgba(252, 182, 159, 0.5);
}

/* 紧凑布局 */
.digital-keyboard.compact-layout {
  padding: 12px;
  min-width: 180px;
}

.compact-layout .grid {
  gap: 1px;
}

.compact-layout button {
  height: 40px !important;
  font-size: 16px !important;
}

/* 最小布局 */
.digital-keyboard.minimal-layout {
  padding: 8px;
  min-width: 160px;
}

.minimal-layout .grid {
  gap: 1px;
}

.minimal-layout button {
  height: 32px !important;
  font-size: 14px !important;
}

.minimal-layout h3 {
  font-size: 14px !important;
}

.minimal-layout p {
  font-size: 12px !important;
}

/* 按钮点击动画 */
@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

/* 响应式适配 */
@media screen and (max-width: 1366px) {
  .digital-keyboard h3 {
    font-size: 16px;
  }

  .digital-keyboard p {
    font-size: 12px;
  }

  .digital-keyboard button {
    font-size: 14px;
  }
}

@media screen and (min-width: 1920px) {
  .digital-keyboard h3 {
    font-size: 20px;
  }

  .digital-keyboard p {
    font-size: 16px;
  }

  .digital-keyboard button {
    font-size: 16px;
    height: 56px;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .digital-keyboard {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .digital-keyboard button {
    transition: none !important;
    animation: none !important;
  }
}
</style>
