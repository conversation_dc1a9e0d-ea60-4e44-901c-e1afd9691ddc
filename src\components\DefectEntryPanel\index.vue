<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import UploadFile from '@/components/UploadFile/index.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import DigitalKeyboard from '@/components/DigitalKeyboard/index.vue'
import ResizableSplitter from '@/components/ResizableSplitter/index.vue'
import { DictionaryType, GlobalEnum } from '@/common/enum'
import { formatLengthMul } from '@/common/format'
import { DigitalKeyboardPresets } from '@/components/DigitalKeyboard/types'
import { breakupUrl, formatUrl } from '@/common/util'
import { addInfoBasicDefect } from '@/api/defectData'
import { useGlobalConfig } from '@/use/useGlobalConfig'

// 定义 Props
const props = withDefaults(defineProps<DefectEntryProps>(), {
  modalMode: false,
  modalTitle: '疵点信息',
  visible: false,
  isOther: false,
  isEdit: false,
  showUpload: true,
  showPosition: true,
  showCategory: false,
  showDefectCode: false,
  showDigitalKeyboard: true,
  showSplitter: true,
  initialSplit: 65,
  extraFields: () => [],
  scoreOptions: () => [1, 2, 3, 4],
  uploadAccept: 'image/*,.pdf,.doc,.docx',
  uploadScene: 'defect',
})
// 定义 Emits
const emits = defineEmits<{
  'update:visible': [value: boolean]
  'update:modelValue': [value: DefectFormData]
  'submit': [data: DefectFormData]
  'cancel': []
  'hide': []
}>()
// 常量定义
const VALID_SCORES = [1, 2, 3, 4] as const
const STEP_POSITION = 1
const STEP_COUNT = 2
const STEP_SCORE = 3
const DEFAULT_SCORE = 1
const DISABLED_NUMBERS_FOR_SCORE = [5, 6, 7, 8, 9, 0]
const INITIAL_POSITION_VALUE = 0
const INITIAL_COUNT_VALUE = 0

// 定义组件的 Props 类型
interface DefectEntryProps {
  /** 是否显示为弹框模式 */
  modalMode?: boolean
  /** 弹框标题 */
  modalTitle?: string
  /** 是否显示弹框 */
  visible?: boolean
  /** 疵点数据 */
  modelValue?: DefectFormData
  /** 是否为"其他"疵点类型 */
  isOther?: boolean
  /** 是否为编辑模式 */
  isEdit?: boolean
  /** 是否显示上传功能 */
  showUpload?: boolean
  /** 是否显示疵点位置 */
  showPosition?: boolean
  /** 是否显示疵点类别 */
  showCategory?: boolean
  /** 是否显示疵点编号 */
  showDefectCode?: boolean
  /** 是否显示数字键盘 */
  showDigitalKeyboard?: boolean
  /** 是否显示分割器 */
  showSplitter?: boolean
  /** 初始分割比例 */
  initialSplit?: number
  /** 额外的表单字段显示配置 */
  extraFields?: string[]
  /** 表单验证规则 */
  customRules?: Record<string, any>
  /** 分数选项 */
  scoreOptions?: number[]
  /** 上传文件类型 */
  uploadAccept?: string
  /** 上传场景 */
  uploadScene?: string
}

// 疵点表单数据类型
interface DefectFormData {
  id?: number
  name?: string
  defect_name?: string
  defect_code?: string
  defect_id?: number
  defect_count?: number | string
  defect_position?: number | string
  measurement_unit_id?: number
  measurement_unit_name?: string
  score?: number | string
  kind_id?: number
  voucher_files?: string[]
  [key: string]: any
}

// 表单引用
const formRef = ref()

// 内部状态
const state = reactive({
  showModal: false,
  form: {
    name: '',
    defect_name: '',
    defect_code: '',
    defect_id: 0,
    defect_count: INITIAL_COUNT_VALUE,
    defect_position: INITIAL_POSITION_VALUE,
    measurement_unit_id: 0,
    measurement_unit_name: '',
    score: DEFAULT_SCORE,
    kind_id: 0,
    voucher_files: [] as string[],
  } as DefectFormData,
})

// 数字键盘相关状态
const editStep = ref(STEP_POSITION)
const splitRatio = ref(props.initialSplit)

// 同步到疵点资料相关状态
const isSyncToDefectData = ref(false)
const isOtherDefect = ref(false) // 是否为其他疵点

// API实例
const { fetchData: addDefectToBasic, success: addDefectSuccess, msg: addDefectMsg } = addInfoBasicDefect()
const { getConfigValue, updateConfigValue } = useGlobalConfig()

// 默认验证规则
const defaultRules = {
  defect_count: [
    { required: true, message: '请输入疵点个数', trigger: 'blur' },
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (value <= 0)
          callback(new Error('疵点个数不能为0'))
        else
          callback()
      },
      trigger: 'blur',
    },
  ],
  defect_name: [{ required: true, message: '请输入疵点名称', trigger: 'blur' }],
  defect_position: [
    { required: true, message: '请输入疵点位置', trigger: 'blur' },
    {
      validator: (_rule: any, value: any, callback: any) => {
        const numValue = Number(value)
        if (numValue <= 0)
          callback(new Error('疵点位置必须大于0'))
        else
          callback()
      },
      trigger: 'blur',
    },
  ],
}

// 合并验证规则
const rules = computed(() => ({
  ...defaultRules,
  ...props.customRules,
}))

// 数字键盘配置
const keyboardConfig = computed(() => {
  const baseDisabledNumbers = {
    [STEP_SCORE]: DISABLED_NUMBERS_FOR_SCORE, // 第3步只允许1-4
  }

  // 动态禁用数字0：只在疵点数量为0时禁用数字0的输入（疵点位置允许0.22这样的输入）
  const currentStepDisabled = [...(baseDisabledNumbers[editStep.value as keyof typeof baseDisabledNumbers] || [])]
  if (editStep.value === STEP_COUNT && (state.form.defect_count === INITIAL_COUNT_VALUE || state.form.defect_count === '0')) {
    if (!currentStepDisabled.includes(0))
      currentStepDisabled.push(0)
  }

  return {
    ...DigitalKeyboardPresets.qualityCheck,
    editStep: editStep.value,
    selectedValue: editStep.value === STEP_SCORE ? state.form.score : undefined,
    disabledNumbers: {
      ...baseDisabledNumbers,
      [editStep.value]: currentStepDisabled,
    },
  }
})

// 计算当前活跃字段的CSS类
function getFieldClass(step: number) {
  return {
    'field-active': editStep.value === step,
    'field-inactive': editStep.value !== step,
  }
}

// 监听 visible 变化
watch(() => props.visible, (newVal) => {
  state.showModal = newVal
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  if (newVal)
    Object.assign(state.form, newVal)
}, { immediate: true, deep: true })

// 监听内部表单变化，同步到外部
watch(() => state.form, (newVal) => {
  emits('update:modelValue', { ...newVal })
}, { deep: true })

// 加载全局配置
async function loadGlobalConfig() {
  try {
    // 使用 useGlobalConfig 获取配置值，自动处理类型转换
    isSyncToDefectData.value = await getConfigValue<boolean>(
      GlobalEnum.IsSyncToDefectData,
      false, // 默认值
    )
  }
  catch (error) {
    console.warn('获取全局配置失败:', error)
    isSyncToDefectData.value = false
  }
}

// 处理同步配置变化的事件
async function handleSyncConfigChange(value: boolean) {
  // 只有在其他疵点模式下才更新全局配置
  if (!isOtherDefect.value)
    return

  try {
    const success = await updateConfigValue(GlobalEnum.IsSyncToDefectData, value)
    if (!success)
      isSyncToDefectData.value = !value
  }
  catch (error) {
    console.warn('更新同步配置失败:', error)
  }
}

// 同步到疵点资料
async function syncToDefectData(formData: any) {
  try {
    const defectBasicData = {
      code: formData.defect_code || '',
      name: formData.defect_name || formData.name,
      measurement_unit_id: formData.measurement_unit_id,
      kind_id: formData.kind_id || 0,
      sort: 0,
      remark: '从疵点录入自动同步',
    }

    await addDefectToBasic(defectBasicData)

    if (addDefectSuccess.value)
      ElMessage.success('疵点信息已同步到疵点资料')

    else
      ElMessage.warning(`同步到疵点资料失败: ${addDefectMsg.value}`)
  }
  catch (error) {
    console.warn('同步到疵点资料失败:', error)
    ElMessage.warning('同步到疵点资料失败')
  }
}

// 显示弹框
async function showDialog(data?: DefectFormData, isOther = false, isAdd = true) {
  state.showModal = true
  emits('update:visible', true)

  // 设置是否为其他疵点
  isOtherDefect.value = isOther

  // 如果是其他疵点，获取全局配置
  if (isOther)
    await loadGlobalConfig()

  if (isAdd && data) {
    state.form = {
      ...data,
      id: undefined,
      defect_count: 0,
      defect_position: 0,
      score: 1,
      defect_id: data.id || 0,
      defect_name: data.name || '',
      voucher_files: [],
    }
  }
  else if (data) {
    state.form = {
      ...data,
      voucher_files: data.voucher_files ? data.voucher_files.map(item => formatUrl(item)) : [],
    }
  }

  // 重置编辑步骤
  editStep.value = 1
}

// 设置疵点位置
function setDefectPosition(position: number) {
  state.form.defect_position = position
}

// 设置表单数据
function setFormData(data: DefectFormData) {
  Object.assign(state.form, data)
}

// 处理提交
async function handleSubmit() {
  if (!formRef.value)
    return

  formRef.value.validate(async (valid: boolean) => {
    if (!valid)
      return

    const submitData = {
      ...state.form,
      defect_id: state.form.defect_id || 0,
      defect_count: Number(state.form.defect_count) || INITIAL_COUNT_VALUE,
      defect_position: props.showPosition ? formatLengthMul(Number(state.form.defect_position) || INITIAL_POSITION_VALUE) : INITIAL_POSITION_VALUE,
      score: Number(state.form.score) || DEFAULT_SCORE,
      voucher_files: state.form.voucher_files?.map(item => breakupUrl(item)) || [],
    }

    // 如果是其他疵点且选择了同步到疵点资料，则调用添加疵点资料接口
    if (isOtherDefect.value && isSyncToDefectData.value)
      await syncToDefectData(submitData)

    emits('submit', submitData)

    if (props.modalMode)
      handleHide()
  })
}

// 处理取消
function handleCancel() {
  emits('cancel')
  if (props.modalMode)
    handleHide()
}

// 处理隐藏
function handleHide() {
  state.showModal = false
  emits('update:visible', false)
  emits('hide')
}

// 处理单位选择变化
function handleUnitSelectChange(val: any) {
  state.form.measurement_unit_name = val.name
}

// 处理上传文件变化
function handleUploadFilesChange(fileList: string[]) {
  state.form.voucher_files = fileList
}

// 数字键盘事件处理
function handleNumberInput(num: number) {
  switch (editStep.value) {
    case STEP_POSITION: // 疵点位置
      if (state.form.defect_position === INITIAL_POSITION_VALUE || state.form.defect_position === '0') {
        // 疵点位置允许输入0（用于0.22这样的小数）
        state.form.defect_position = String(num)
      }
      else {
        state.form.defect_position += String(num)
      }
      break
    case STEP_COUNT: // 疵点数量
      if (state.form.defect_count === INITIAL_COUNT_VALUE || state.form.defect_count === '0') {
        // 疵点个数不能为0，如果输入0则忽略
        if (num !== 0)
          state.form.defect_count = String(num)
      }
      else {
        state.form.defect_count += String(num)
      }
      break
    case STEP_SCORE: // 疵点打分 - 只允许1234
      if (VALID_SCORES.includes(num as typeof VALID_SCORES[number]))
        state.form.score = num

      break
  }
}

function handleDotInput() {
  // 只有在疵点位置步骤才允许输入小数点
  if (editStep.value === STEP_POSITION && !String(state.form.defect_position).includes('.'))
    state.form.defect_position += '.'
}

// 处理数字键盘确定按钮点击 - 相当于点击外部的录入按钮
function handleKeyboardSubmit() {
  // 触发录入操作，相当于点击外部的录入按钮
  handleSubmit()
}

function handleBackspace() {
  switch (editStep.value) {
    case STEP_POSITION: {
      const posStr = String(state.form.defect_position)
      if (posStr.length > 0)
        state.form.defect_position = posStr.slice(0, -1) || INITIAL_POSITION_VALUE

      break
    }
    case STEP_COUNT:{
      const countStr = String(state.form.defect_count)
      if (countStr.length > 0)
        state.form.defect_count = countStr.slice(0, -1) || INITIAL_COUNT_VALUE

      break
    }
  }
}

function handleStepChange(step: number) {
  editStep.value = step
}

// 处理分割器变化
function handleSplitChange(split: number) {
  splitRatio.value = split
}

// 暴露方法给父组件
defineExpose({
  showDialog,
  setDefectPosition,
  setFormData,
  handleSubmit,
  state,
  formRef,
})
</script>

<template>
  <!-- 弹框模式 -->
  <vxe-modal
    v-if="modalMode"
    v-model="state.showModal"
    :title="modalTitle"
    width="900"
    height="auto"
    show-footer
    :mask="false"
    :lock-view="false"
    :esc-closable="true"
    resize
    @hide="handleHide"
  >
    <!-- 弹框内容使用分割布局 -->
    <div class="defect-entry-modal-content">
      <ResizableSplitter
        v-if="showSplitter && showDigitalKeyboard"
        direction="horizontal"
        :initial-split="splitRatio"
        :min-size="30"
        :max-size="80"
        @split-change="handleSplitChange"
      >
        <template #left>
          <!-- 左侧表单区域 -->
          <div class="form-panel">
            <el-form
              ref="formRef"
              :model="state.form"
              :rules="rules"
              label-width="100px"
              label-position="left"
              size="default"
              require-asterisk-position="right"
            >
              <!-- 表单内容 -->
              <slot name="form-content">
                <!-- 疵点编号 -->
                <el-form-item v-if="showDefectCode && isOther" class="px-[12px]" label="疵点编号" prop="defect_code">
                  <el-input
                    v-model="state.form.defect_code"
                    placeholder="疵点编号(非必填)"
                    clearable
                  />
                </el-form-item>

                <!-- 疵点名称 -->
                <el-form-item class="px-[12px]" label="疵点名称" prop="defect_name">
                  <span v-if="!isOther" class="text-gray-800">{{ state.form.defect_name || state.form.name }}</span>
                  <el-input
                    v-else
                    v-model="state.form.defect_name"
                    placeholder="疵点名称"
                    clearable
                  />
                </el-form-item>

                <!-- 单位名称 -->
                <el-form-item class="px-[12px]" label="单位名称" prop="measurement_unit_id">
                  <SelectComponents
                    v-if="isOther"
                    v-model="state.form.measurement_unit_id"
                    style="width: 200px"
                    api="getInfoBaseMeasurementUnitList"
                    label-field="name"
                    value-field="id"
                    clearable
                    @change-value="handleUnitSelectChange"
                  />
                  <span v-else>
                    {{ state.form.measurement_unit_name }}
                  </span>
                </el-form-item>

                <!-- 疵点类别 -->
                <el-form-item v-if="showCategory && isOther" class="px-[12px]" label="疵点类别" prop="kind_id">
                  <SelectComponents
                    v-model="state.form.kind_id"
                    style="width: 200px"
                    api="GetDictionaryDetailEnumListApi"
                    placeholder="请选择疵点种类(非必填)"
                    label-field="name"
                    value-field="id"
                    :query="{ dictionary_id: DictionaryType.defectKind }"
                    clearable
                  />
                </el-form-item>

                <!-- 疵点位置 -->
                <el-form-item
                  v-if="showPosition"
                  label="疵点位置"
                  prop="defect_position"
                  :class="getFieldClass(1)"
                  class="form-field-item"
                >
                  <div class="flex items-center gap-2">
                    <span class="text-gray-500">第</span>
                    <el-input-number
                      v-model="state.form.defect_position"
                      :min="0"
                      :precision="2"
                      placeholder="请输入疵点位置"
                      style="width: 200px"
                      readonly
                    />
                    <span class="text-gray-500">米</span>
                  </div>
                  <el-text type="info" size="small" style="margin-left: 10px;">
                    (数字键盘输入)
                  </el-text>
                </el-form-item>

                <!-- 疵点数量 -->
                <el-form-item
                  label="疵点数量"
                  prop="defect_count"
                  :class="getFieldClass(2)"
                  class="form-field-item"
                >
                  <div class="flex items-center gap-2">
                    <el-input-number
                      v-model="state.form.defect_count"
                      :min="0"
                      :precision="0"
                      placeholder="请输入疵点数量"
                      style="width: 150px"
                      readonly
                    />
                    <span class="text-gray-500">{{ state.form.measurement_unit_name }}</span>
                  </div>
                  <el-text type="info" size="small" style="margin-left: 10px;">
                    (数字键盘输入)
                  </el-text>
                </el-form-item>

                <!-- 疵点分数 -->
                <el-form-item
                  label="分数"
                  prop="score"
                  :class="getFieldClass(3)"
                  class="form-field-item"
                >
                  <el-radio-group v-model="state.form.score" disabled>
                    <el-radio-button
                      v-for="score in scoreOptions"
                      :key="score"
                      :label="score"
                      :value="score"
                    />
                  </el-radio-group>
                  <el-text type="info" size="small" style="margin-left: 10px;">
                    (数字键盘选择)
                  </el-text>
                </el-form-item>

                <!-- 上传文件 -->
                <el-form-item v-if="showUpload" label-position="top" label="上传凭证" prop="voucher_files">
                  <UploadFile
                    v-model:file-list="state.form.voucher_files"
                    :dragable="true"
                    :multiple="true"
                    :accept="uploadAccept"
                    :secene="uploadScene"
                    :auto-upload="true"
                    @on-upload-success="handleUploadFilesChange"
                  />
                </el-form-item>
              </slot>
            </el-form>
          </div>
        </template>

        <template #right>
          <!-- 右侧数字键盘区域 -->
          <div class="keyboard-panel">
            <DigitalKeyboard
              :edit-step="editStep"
              :step-titles="keyboardConfig.stepTitles"
              :step-descriptions="keyboardConfig.stepDescriptions"
              :show-dot="keyboardConfig.showDot"
              :show-clear="false"
              :show-submit="true"
              :show-backspace="true"
              :show-step-navigation="true"
              :total-steps="keyboardConfig.totalSteps"
              :disabled-numbers="keyboardConfig.disabledNumbers"
              :selected-value="keyboardConfig.selectedValue"
              @number-input="handleNumberInput"
              @dot-input="handleDotInput"
              @submit="handleKeyboardSubmit"
              @backspace="handleBackspace"
              @step-change="handleStepChange"
            />
          </div>
        </template>
      </ResizableSplitter>

      <!-- 无分割器时的简单布局 -->
      <div v-else class="simple-layout">
        <el-form
          ref="formRef"
          :model="state.form"
          :rules="rules"
          label-width="100px"
          label-position="right"
          size="default"
        >
          <!-- 简化的表单内容 -->
          <slot name="simple-form" />
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="flex w-full justify-between">
        <el-checkbox
          v-if="isOtherDefect"
          v-model="isSyncToDefectData"
          class="mr-2"
          size="large"
          @change="handleSyncConfigChange"
        >
          同步到疵点资料
        </el-checkbox>
        <div class="flex flex-1 justify-end">
          <el-button size="large" @click="handleCancel">
            取消
          </el-button>
          <el-button size="large" type="primary" @click="handleSubmit">
            录入
          </el-button>
        </div>
      </div>
    </template>
  </vxe-modal>

  <!-- 面板模式 -->
  <div v-else class="defect-entry-panel">
    <slot name="header" />

    <!-- 面板内容 -->
    <div class="panel-content">
      <el-form
        ref="formRef"
        :model="state.form"
        :rules="rules"
        label-width="100px"
        label-position="left"
        size="large"
      >
        <!-- 面板表单内容 -->
        <slot name="panel-form" />
      </el-form>
    </div>

    <slot name="footer" />

    <!-- 操作按钮 -->
    <div v-if="!$slots.footer" class="mt-4 flex gap-2">
      <el-button @click="handleCancel">
        取消
      </el-button>
      <el-button type="primary" @click="handleSubmit">
        确定
      </el-button>
    </div>
  </div>
</template>

<style scoped>
.defect-entry-panel {
  width: 100%;
}

.defect-entry-modal-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.form-panel {
  padding: 16px;
  overflow-y: auto;
  height: 100%;
}

.keyboard-panel {
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.simple-layout {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
}

/* 表单字段高亮效果 */
.form-field-item {
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 12px;
  margin: 8px 0;
  position: relative;
}

.form-field-item.field-active {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.08) 0%, rgba(64, 158, 255, 0.04) 100%);
  border: 2px solid #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.form-field-item.field-active::before {
  content: '';
  position: absolute;
  left: -4px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 60%;
  background: linear-gradient(180deg, #409eff 0%, #66b3ff 100%);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.form-field-item.field-inactive {
  background: transparent;
  border: 2px solid transparent;
  box-shadow: none;
  transform: translateY(0);
}

/* 活跃字段内的输入框样式增强 */
.form-field-item.field-active :deep(.el-input-number) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.form-field-item.field-active :deep(.el-input-number:hover) {
  border-color: #66b3ff;
}

/* 分数按钮组的整体增强样式 */
.form-field-item.field-active :deep(.el-radio-group) {
  padding: 8px;
  border-radius: 8px;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(64, 158, 255, 0.02) 100%);
  border: 1px solid rgba(64, 158, 255, 0.2);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  position: relative;
  overflow: visible;
}

/* 分数按钮组的发光效果 */
.form-field-item.field-active :deep(.el-radio-group::before) {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #409eff, #66b3ff, #409eff);
  border-radius: 10px;
  z-index: -1;
  opacity: 0.3;
  filter: blur(4px);
  animation: scoreGroupGlow 2s ease-in-out infinite;
}

@keyframes scoreGroupGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

/* 分数按钮的基础样式重置 */
.form-field-item.field-active :deep(.el-radio-button) {
  margin: 0 4px;
  transition: all 0.3s ease;
}

.form-field-item.field-active :deep(.el-radio-button__inner) {
  border: 2px solid #d9d9d9;
  background: #ffffff;
  color: #606266;
  font-weight: 600;
  font-size: 16px;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 分数按钮悬停效果 */
.form-field-item.field-active :deep(.el-radio-button__inner:hover) {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.05);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.2);
}

/* 选中状态的分数按钮 - 主要增强效果 */
.form-field-item.field-active :deep(.el-radio-button.is-active .el-radio-button__inner) {
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  border-color: #409eff;
  color: #ffffff;
  font-weight: 700;
  transform: scale(1.05) translateY(-2px);
  box-shadow:
    0 6px 16px rgba(64, 158, 255, 0.4),
    0 0 0 3px rgba(64, 158, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
}

/* 选中按钮的发光效果 */
.form-field-item.field-active :deep(.el-radio-button.is-active .el-radio-button__inner::before) {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(45deg, #409eff, #66b3ff, #409eff);
  border-radius: 9px;
  z-index: -1;
  opacity: 0.6;
  filter: blur(6px);
  animation: selectedScoreGlow 1.5s ease-in-out infinite;
}

@keyframes selectedScoreGlow {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.1);
  }
}

/* 选中按钮的内部光泽效果 */
.form-field-item.field-active :deep(.el-radio-button.is-active .el-radio-button__inner::after) {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: scoreShine 2s ease-in-out infinite;
}

@keyframes scoreShine {
  0% { left: -100%; }
  50%, 100% { left: 100%; }
}

/* 非选中按钮在活跃状态下的样式 */
.form-field-item.field-active :deep(.el-radio-button:not(.is-active) .el-radio-button__inner) {
  background: #f8f9fa;
  border-color: #e9ecef;
  color: #6c757d;
  opacity: 0.8;
}

/* 非选中按钮悬停时的增强效果 */
.form-field-item.field-active :deep(.el-radio-button:not(.is-active) .el-radio-button__inner:hover) {
  background: rgba(64, 158, 255, 0.08);
  border-color: #409eff;
  color: #409eff;
  opacity: 1;
}

/* 活跃字段的标签样式 */
.form-field-item.field-active :deep(.el-form-item__label) {
  color: #409eff;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(64, 158, 255, 0.1);
}

/* 提示文字在活跃状态下的样式 */
.form-field-item.field-active .el-text {
  color: #409eff !important;
  font-weight: 500;
}

/* 脉冲动画效果 */
.form-field-item.field-active {
  animation: fieldPulse 2s ease-in-out infinite;
}

@keyframes fieldPulse {
  0% {
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  }
  50% {
    box-shadow: 0 6px 20px rgba(64, 158, 255, 0.25);
  }
  100% {
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .form-field-item.field-active {
    background: linear-gradient(135deg, rgba(64, 158, 255, 0.15) 0%, rgba(64, 158, 255, 0.08) 100%);
    border-color: #66b3ff;
  }

  .form-field-item.field-active::before {
    background: linear-gradient(180deg, #66b3ff 0%, #409eff 100%);
  }

  .form-field-item.field-active :deep(.el-form-item__label) {
    color: #66b3ff;
  }

  .form-field-item.field-active .el-text {
    color: #66b3ff !important;
  }

  /* 暗色主题下的分数按钮组样式 */
  .form-field-item.field-active :deep(.el-radio-group) {
    background: linear-gradient(135deg, rgba(102, 179, 255, 0.08) 0%, rgba(102, 179, 255, 0.04) 100%);
    border-color: rgba(102, 179, 255, 0.3);
    box-shadow: 0 2px 8px rgba(102, 179, 255, 0.15);
  }

  .form-field-item.field-active :deep(.el-radio-group::before) {
    background: linear-gradient(45deg, #66b3ff, #409eff, #66b3ff);
  }

  /* 暗色主题下的分数按钮基础样式 */
  .form-field-item.field-active :deep(.el-radio-button__inner) {
    background: #2c3e50;
    border-color: #4a5568;
    color: #e2e8f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .form-field-item.field-active :deep(.el-radio-button__inner:hover) {
    background: rgba(102, 179, 255, 0.1);
    border-color: #66b3ff;
  }

  /* 暗色主题下选中状态的分数按钮 */
  .form-field-item.field-active :deep(.el-radio-button.is-active .el-radio-button__inner) {
    background: linear-gradient(135deg, #66b3ff 0%, #409eff 100%);
    border-color: #66b3ff;
    color: #ffffff;
    box-shadow:
      0 6px 16px rgba(102, 179, 255, 0.5),
      0 0 0 3px rgba(102, 179, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  .form-field-item.field-active :deep(.el-radio-button.is-active .el-radio-button__inner::before) {
    background: linear-gradient(45deg, #66b3ff, #409eff, #66b3ff);
    opacity: 0.8;
  }

  /* 暗色主题下非选中按钮样式 */
  .form-field-item.field-active :deep(.el-radio-button:not(.is-active) .el-radio-button__inner) {
    background: #1a202c;
    border-color: #2d3748;
    color: #a0aec0;
  }

  .form-field-item.field-active :deep(.el-radio-button:not(.is-active) .el-radio-button__inner:hover) {
    background: rgba(102, 179, 255, 0.12);
    border-color: #66b3ff;
    color: #66b3ff;
  }
}

/* 高对比度模式适配 */
@media (prefers-contrast: high) {
  .form-field-item.field-active {
    border-width: 3px;
    border-color: #0066cc;
    background: rgba(0, 102, 204, 0.1);
  }

  .form-field-item.field-active::before {
    background: #0066cc;
    width: 6px;
  }

  /* 高对比度模式下的分数按钮组 */
  .form-field-item.field-active :deep(.el-radio-group) {
    border: 2px solid #0066cc;
    background: rgba(0, 102, 204, 0.05);
  }

  .form-field-item.field-active :deep(.el-radio-group::before) {
    display: none; /* 禁用发光效果 */
  }

  /* 高对比度模式下的分数按钮 */
  .form-field-item.field-active :deep(.el-radio-button__inner) {
    border: 2px solid #333333;
    background: #ffffff;
    color: #000000;
    font-weight: 700;
  }

  .form-field-item.field-active :deep(.el-radio-button__inner:hover) {
    border-color: #0066cc;
    background: #f0f8ff;
  }

  /* 高对比度模式下选中状态的分数按钮 */
  .form-field-item.field-active :deep(.el-radio-button.is-active .el-radio-button__inner) {
    background: #0066cc;
    border-color: #0066cc;
    color: #ffffff;
    font-weight: 900;
    box-shadow: 0 0 0 3px #ffff00; /* 黄色外框增强对比 */
  }

  .form-field-item.field-active :deep(.el-radio-button.is-active .el-radio-button__inner::before),
  .form-field-item.field-active :deep(.el-radio-button.is-active .el-radio-button__inner::after) {
    display: none; /* 禁用装饰效果 */
  }

  /* 高对比度模式下非选中按钮 */
  .form-field-item.field-active :deep(.el-radio-button:not(.is-active) .el-radio-button__inner) {
    background: #f8f8f8;
    border-color: #666666;
    color: #333333;
  }
}

/* 减少动画模式适配 */
@media (prefers-reduced-motion: reduce) {
  .form-field-item {
    transition: none !important;
    animation: none !important;
  }

  .form-field-item.field-active {
    transform: none !important;
  }

  /* 减少动画模式下的分数按钮 */
  .form-field-item.field-active :deep(.el-radio-group),
  .form-field-item.field-active :deep(.el-radio-group::before),
  .form-field-item.field-active :deep(.el-radio-button),
  .form-field-item.field-active :deep(.el-radio-button__inner),
  .form-field-item.field-active :deep(.el-radio-button.is-active .el-radio-button__inner),
  .form-field-item.field-active :deep(.el-radio-button.is-active .el-radio-button__inner::before),
  .form-field-item.field-active :deep(.el-radio-button.is-active .el-radio-button__inner::after) {
    transition: none !important;
    animation: none !important;
    transform: none !important;
  }

  /* 保持选中状态的基本视觉效果，但移除动画 */
  .form-field-item.field-active :deep(.el-radio-button.is-active .el-radio-button__inner) {
    background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
    border-color: #409eff;
    color: #ffffff;
    font-weight: 700;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4), 0 0 0 3px rgba(64, 158, 255, 0.2);
  }
}

/* 同步选项样式 */
.sync-option {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.sync-option :deep(.el-checkbox) {
  font-size: 14px;
  font-weight: 500;
}

.sync-option :deep(.el-checkbox__label) {
  color: #409eff;
}

/* 响应式适配 */
@media screen and (max-width: 768px) {
  .defect-entry-modal-content {
    flex-direction: column;
  }

  .form-panel,
  .keyboard-panel {
    padding: 8px;
  }

  .form-field-item {
    padding: 8px;
    margin: 4px 0;
  }

  .form-field-item.field-active {
    transform: translateY(-1px);
  }

  .sync-option {
    margin-bottom: 12px;
    padding: 8px;
  }

  .footer-buttons {
    gap: 8px;
  }
}

@media screen and (min-width: 1920px) {
  .form-field-item {
    padding: 16px;
    margin: 12px 0;
  }

  .form-field-item.field-active {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(64, 158, 255, 0.2);
  }

  .sync-option {
    margin-bottom: 20px;
    padding: 16px;
  }

  .footer-buttons {
    gap: 16px;
  }
}
</style>
