// 数字键盘组件配置类型
export interface DigitalKeyboardConfig {
  /** 当前编辑步骤 */
  editStep?: number
  /** 步骤标题映射 */
  stepTitles?: Record<number, string>
  /** 步骤描述映射 */
  stepDescriptions?: Record<number, string>
  /** 是否显示小数点按钮 */
  showDot?: boolean
  /** 是否显示清除按钮 */
  showClear?: boolean
  /** 是否显示退格按钮 */
  showBackspace?: boolean
  /** 是否显示确认按钮 */
  showConfirm?: boolean
  /** 是否显示取消按钮 */
  showCancel?: boolean
  /** 是否显示步骤导航 */
  showStepNavigation?: boolean
  /** 总步骤数 */
  totalSteps?: number
  /** 禁用的数字（针对特定步骤） */
  disabledNumbers?: Record<number, number[]>
  /** 当前选中的值（用于高亮显示） */
  selectedValue?: number | string
  /** 键盘布局模式 */
  layout?: 'standard' | 'compact' | 'minimal'
  /** 是否显示进度指示器 */
  showProgress?: boolean
}

// 数字键盘事件类型
export interface DigitalKeyboardEvents {
  /** 数字输入事件 */
  'number-input': (number: number) => void
  /** 小数点输入事件 */
  'dot-input': () => void
  /** 清除事件 */
  'clear': () => void
  /** 退格事件 */
  'backspace': () => void
  /** 确认事件 */
  'confirm': () => void
  /** 取消事件 */
  'cancel': () => void
  /** 步骤变化事件 */
  'step-change': (step: number) => void
}

// 数字键盘实例方法类型
export interface DigitalKeyboardInstance {
  /** 处理数字点击 */
  handleNumberClick: (num: number) => void
  /** 处理小数点点击 */
  handleDotClick: () => void
  /** 处理清除点击 */
  handleClearClick: () => void
  /** 处理退格点击 */
  handleBackspaceClick: () => void
  /** 处理确认点击 */
  handleConfirmClick: () => void
  /** 处理取消点击 */
  handleCancelClick: () => void
  /** 处理上一步 */
  handlePrevStep: () => void
  /** 处理下一步 */
  handleNextStep: () => void
}

// 预设配置
export const DigitalKeyboardPresets = {
  // 疵点位置输入配置
  position: {
    editStep: 1,
    stepTitles: { 1: '疵点位置' },
    stepDescriptions: { 1: '请输入位置（米）' },
    showDot: true,
    showClear: true,
    showBackspace: true,
    showConfirm: false,
    showCancel: false,
    showStepNavigation: false,
    totalSteps: 1,
    disabledNumbers: {},
    layout: 'standard',
    showProgress: false,
  } as DigitalKeyboardConfig,

  // 疵点数量输入配置
  count: {
    editStep: 1,
    stepTitles: { 1: '疵点数量' },
    stepDescriptions: { 1: '请输入个数' },
    showDot: false,
    showClear: true,
    showBackspace: true,
    showConfirm: false,
    showCancel: false,
    showStepNavigation: false,
    totalSteps: 1,
    disabledNumbers: {},
    layout: 'standard',
    showProgress: false,
  } as DigitalKeyboardConfig,

  // 疵点分数选择配置
  score: {
    editStep: 1,
    stepTitles: { 1: '疵点分数' },
    stepDescriptions: { 1: '请选择分数' },
    showDot: false,
    showClear: false,
    showBackspace: false,
    showConfirm: false,
    showCancel: false,
    showStepNavigation: false,
    totalSteps: 1,
    disabledNumbers: { 1: [5, 6, 7, 8, 9, 0] }, // 只允许1-4
    layout: 'standard',
    showProgress: false,
  } as DigitalKeyboardConfig,

  // 成品质检页面三步骤配置
  qualityCheck: {
    editStep: 1,
    stepTitles: {
      1: '疵点位置',
      2: '疵点数量',
      3: '疵点分数',
    },
    stepDescriptions: {
      1: '请输入位置',
      2: '请输入个数',
      3: '请选择分数',
    },
    showDot: true,
    showClear: true,
    showBackspace: true,
    showConfirm: false,
    showCancel: false,
    showStepNavigation: true,
    totalSteps: 3,
    disabledNumbers: {
      3: [5, 6, 7, 8, 9, 0], // 第3步只允许1-4
    },
    layout: 'standard',
    showProgress: true,
  } as DigitalKeyboardConfig,

  // 验布称重页面配置
  inspection: {
    editStep: 1,
    stepTitles: {
      1: '疵点位置',
      2: '疵点数量',
    },
    stepDescriptions: {
      1: '请输入位置（米）',
      2: '请输入个数',
    },
    showDot: true,
    showClear: true,
    showBackspace: true,
    showConfirm: true,
    showCancel: true,
    showStepNavigation: true,
    totalSteps: 2,
    disabledNumbers: {},
    layout: 'standard',
    showProgress: true,
  } as DigitalKeyboardConfig,

  // 紧凑模式配置
  compact: {
    editStep: 1,
    stepTitles: { 1: '数字输入' },
    stepDescriptions: { 1: '请输入数字' },
    showDot: true,
    showClear: true,
    showBackspace: true,
    showConfirm: false,
    showCancel: false,
    showStepNavigation: false,
    totalSteps: 1,
    disabledNumbers: {},
    layout: 'compact',
    showProgress: false,
  } as DigitalKeyboardConfig,

  // 最小模式配置
  minimal: {
    editStep: 1,
    stepTitles: { 1: '输入' },
    stepDescriptions: { 1: '请输入' },
    showDot: false,
    showClear: false,
    showBackspace: true,
    showConfirm: false,
    showCancel: false,
    showStepNavigation: false,
    totalSteps: 1,
    disabledNumbers: {},
    layout: 'minimal',
    showProgress: false,
  } as DigitalKeyboardConfig,
}

// 数字键盘步骤管理器
export class DigitalKeyboardStepManager {
  private currentStep = 1
  private totalSteps = 3
  private stepData: Record<number, any> = {}

  constructor(totalSteps = 3) {
    this.totalSteps = totalSteps
  }

  // 获取当前步骤
  getCurrentStep(): number {
    return this.currentStep
  }

  // 设置当前步骤
  setCurrentStep(step: number): boolean {
    if (step >= 1 && step <= this.totalSteps) {
      this.currentStep = step
      return true
    }
    return false
  }

  // 下一步
  nextStep(): boolean {
    if (this.currentStep < this.totalSteps) {
      this.currentStep++
      return true
    }
    return false
  }

  // 上一步
  prevStep(): boolean {
    if (this.currentStep > 1) {
      this.currentStep--
      return true
    }
    return false
  }

  // 是否是第一步
  isFirstStep(): boolean {
    return this.currentStep === 1
  }

  // 是否是最后一步
  isLastStep(): boolean {
    return this.currentStep === this.totalSteps
  }

  // 设置步骤数据
  setStepData(step: number, data: any): void {
    this.stepData[step] = data
  }

  // 获取步骤数据
  getStepData(step: number): any {
    return this.stepData[step]
  }

  // 获取所有步骤数据
  getAllStepData(): Record<number, any> {
    return { ...this.stepData }
  }

  // 清除步骤数据
  clearStepData(step?: number): void {
    if (step !== undefined)
      delete this.stepData[step]
    else
      this.stepData = {}
  }

  // 重置到第一步
  reset(): void {
    this.currentStep = 1
    this.stepData = {}
  }

  // 获取进度百分比
  getProgress(): number {
    return (this.currentStep / this.totalSteps) * 100
  }
}

// 数字键盘输入处理器
export class DigitalKeyboardInputHandler {
  private value = ''
  private maxLength = 10
  private allowDecimal = true

  constructor(options: { maxLength?: number, allowDecimal?: boolean } = {}) {
    this.maxLength = options.maxLength || 10
    this.allowDecimal = options.allowDecimal !== false
  }

  // 输入数字
  inputNumber(num: number): string {
    if (this.value.length >= this.maxLength)
      return this.value

    if (this.value === '0' && num !== 0)
      this.value = String(num)
    else
      this.value += String(num)

    return this.value
  }

  // 输入小数点
  inputDot(): string {
    if (!this.allowDecimal || this.value.includes('.'))
      return this.value

    if (this.value === '')
      this.value = '0.'
    else
      this.value += '.'

    return this.value
  }

  // 退格
  backspace(): string {
    if (this.value.length > 0)
      this.value = this.value.slice(0, -1)

    return this.value
  }

  // 清除
  clear(): string {
    this.value = ''
    return this.value
  }

  // 获取当前值
  getValue(): string {
    return this.value
  }

  // 设置值
  setValue(value: string): string {
    this.value = value
    return this.value
  }

  // 获取数字值
  getNumberValue(): number {
    return Number.parseFloat(this.value) || 0
  }

  // 验证值是否有效
  isValid(): boolean {
    if (this.value === '' || this.value === '.')
      return false

    return !Number.isNaN(Number.parseFloat(this.value))
  }
}

// 默认配置
export const defaultDigitalKeyboardConfig: DigitalKeyboardConfig = {
  editStep: 1,
  stepTitles: {
    1: '疵点位置',
    2: '疵点数量',
    3: '疵点分数',
  },
  stepDescriptions: {
    1: '请输入位置',
    2: '请输入个数',
    3: '请选择分数',
  },
  showDot: true,
  showClear: true,
  showBackspace: true,
  showConfirm: false,
  showCancel: false,
  showStepNavigation: true,
  totalSteps: 3,
  disabledNumbers: {
    3: [5, 6, 7, 8, 9, 0], // 第3步只允许1-4
  },
  selectedValue: undefined,
  layout: 'standard',
  showProgress: true,
}
