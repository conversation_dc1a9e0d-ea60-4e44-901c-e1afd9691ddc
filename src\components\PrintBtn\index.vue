<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { Printer } from '@element-plus/icons-vue'
import { ref } from 'vue'
import { hiprint } from 'vue-plugin-hiprint'
import { cloneDeep } from 'lodash-es'
import type { templateTypeParam } from './formatData'
import formatData from './formatData'
import printApi from '@/api/printInit'
import { GetPrintTemplate } from '@/api/print'
import { getFilterData } from '@/common/util'

export interface PrintBtnProps {
  list?: any
  type: templateTypeParam // 打印的数据处理模板
  tid: number
  btnText?: string
  api?: keyof typeof printApi
  id?: string | number

  order_type?: number
  btnType?: string
  query?: object
}

const props = withDefaults(defineProps<PrintBtnProps>(), {
  btnText: '打印',
  btnType: 'default',
})
const emits = defineEmits(['onPrint'])
//
const { fetchData, data: templateDataState } = GetPrintTemplate()
const hiprintTemplate = ref()

async function getDataTem() {
  await fetchData({ id: props.tid })
  const template = templateDataState.value?.template
    ? JSON.parse(templateDataState.value.template)
    : {}
  const res = await formatPrintData(cloneDeep(template))

  hiprintTemplate.value = new hiprint.PrintTemplate({ template: res })
}

const detailLoading = ref(false)
async function onPrint() {
  if (props.type == null)
    return ElMessage.error('未绑定数据处理策略')

  emits('onPrint')
  if (props.api) {
    const { fetchData: fetchData1, data: detailData, msg: detailError, success: detailSuccess } = printApi[props.api]()
    detailLoading.value = true
    Promise.all([
      fetchData1(getFilterData({
        id: props.id,
        order_type: props.order_type,
        ...(props.query || {}),
      })),
      getDataTem(),
    ]).then(() => {
      detailLoading.value = false
      if (detailError.value && !detailSuccess.value) {
        ElMessage.error(detailError.value)
        return new Promise(() => {})
      }
      hiprintTemplate.value.print(
        formatData({ type: props.type, data: detailData.value }),
      )
    })
      .catch(() => {
        detailLoading.value = false
      })
  }
  else {
    await getDataTem()
    if (!props.list || (Array.isArray(props.list) && props.list?.length <= 0))
      return ElMessage.error('请选择要打印的数据')
    hiprintTemplate.value.print(
      formatData({ type: props.type, data: props.list }),
    )
  }
}

// 处理打印数据
function formatPrintData(template) {
  const VARNAME = '%s' // 变量名
  try {
    template.panels.forEach((panelItem) => {
      panelItem.printElements.forEach((printElementItem) => {
        // TODO: 兼容有options.formatter的情况
        if (['text', 'longText'].includes(printElementItem.printElementType.type) && !printElementItem.options.formatter) {
          const curOptions = printElementItem.options
          /**
           * 若title里面含有VARNAME，则为拼接字符串
           * 例:title为'销售员:%s,部门:%s'，field为'salesman,department'，则显示的内容为`销售员:${templateData.salesman},部门:${templateData.department}`
           * 把:前面的作为标题,后面的作为内容(formatter)
           */
          const titleAndContent = curOptions.title || ''
          if (titleAndContent.includes(VARNAME)) {
            const fields = curOptions.field?.split(/[,，]/) || [] // 字段名根据,分割
            let newContent = ''
            let newTitle = titleAndContent

            // 内容与标题根据首个:隔开
            const separatorIndex = titleAndContent.search(/[:：]/)
            if (separatorIndex !== -1) {
              newTitle = titleAndContent.slice(0, separatorIndex)
              newContent = titleAndContent.slice(separatorIndex + 1)
            }

            // 如果title含有VARNAME，则把标题放到formatter函数里
            if (newTitle.includes(VARNAME)) {
              newContent = `${newTitle}${newContent}`
              newTitle = ''
              printElementItem.printElementType.title = '' // 清空默认title
            }

            // 将content里的VARNAME按fields数组的顺序逐个替换模板字符的templateData[xx]
            fields.forEach((field) => {
              newContent = newContent.replace(VARNAME, `\${templateData.${field}}`)
            })
            // 把title多余的VARNAME去掉
            newContent = newContent.replace(VARNAME, '')

            // 把内容放到formatter函数里
            if (newContent) {
              const customFormatter = `function (title, value, options, templateData, target) {
                return \`${newContent}\`
              }`
              let newFormatter = customFormatter.toString()
              // 解码转义字符
              newFormatter = newFormatter.replace(/\\u[\dA-Fa-f]{4}/g, (match) => {
                return String.fromCharCode(Number.parseInt(match.slice(2), 16))
              })
              newFormatter = newFormatter.replace('${newContent}', newContent) // 把newContent的模板字符应用

              curOptions.formatter = newFormatter
            }

            curOptions.title = newTitle

            printElementItem.options = curOptions
          }
        }
      })
    })
  }
  catch (error) {

  }

  return template
}
</script>

<template>
  <el-button
    v-if="props.btnType !== 'text'"
    :icon="Printer"
    :type="props.btnType === 'text' ? 'primary' : 'default'"
    :text="props.btnType === 'text'"
    :link="props.btnType === 'text'"
    :loading="detailLoading"
    @click="onPrint"
  >
    {{ props.btnText }}
  </el-button>
  <el-link
    v-else
    :icon="Printer"
    :type="props.btnType === 'text' ? 'primary' : 'default'"
    :underline="false"
    :loading="detailLoading"
    @click="onPrint"
  >
    {{ props.btnText }}
  </el-link>
</template>
