<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import { cancelApprovedSheetOfProductionPlan, checkSheetOfProductionPlan, deleteCancelSheetOfProductionPlan, getSheetOfProductionPlan, rejectSheetOfProductionPlan } from '@/api/sheetOfProductionPlan'
import { formatDate, formatTwoDecimalsDiv, formatUnitPriceDiv, formatWeightDiv } from '@/common/format'
import { deleteToast } from '@/common/util'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import StatusColumn from '@/components/StatusColumn/index.vue'
import Table from '@/components/Table.vue'
import { sumNum } from '@/util/tableFooterCount'

const route = useRoute()
const form_options = [
  {
    text: '营销体系',
    key: 'sale_system_name',
  },
  {
    text: '织厂名称',
    key: 'biz_unit_name',
  },
  {
    text: '织厂跟单',
    key: 'biz_unit_order_follower_name',
  },
  {
    text: '跟单电话',
    key: 'biz_unit_order_follower_phone',
  },
  {
    text: '交坯日期',
    key: 'receipt_grey_fabric_date',
  },
  {
    text: '收坯地址',
    key: 'receipt_grey_fabric_address',
    copies: 2,
  },
  {
    text: '客户PO号',
    key: 'customer_po_num',
  },
  {
    text: '单据备注',
    key: 'remark',
    copies: 2,
  },
]
const state = reactive<any>({
  form: {
    sale_system_name: '',
    biz_unit_name: '',
    biz_unit_order_follower_name: '',
    biz_unit_order_follower_phone: '',
    receipt_grey_fabric_date: '',
    receipt_grey_fabric_address: '',
    remark: '',
  },
  greyList: [],

  allList: [],
  order_no: 0,
  status_name: '',
  status: 0,
})
const { fetchData: detailFetch, data, success: detailSuccess, msg: detailMsg } = getSheetOfProductionPlan()

onMounted(() => {
  getData()
})
async function getData() {
  await detailFetch({ id: route.query.id })
  if (detailSuccess.value) {
    const { summary_detail = [], detail = [] } = data.value
    state.greyList = detail.map((item: any) => {
      return {
        ...item,
        production_plan_type_name: item.production_plan_type_name.join(','),
        grey_fabric_gram_weight: item.grey_fabric_gram_weight,
        finish_product_gram_weight: item.finish_product_gram_weight,
        plan_weight: formatWeightDiv(Number(item.plan_weight)),
        weight_of_fabric: `${formatWeightDiv(Number(item.weight_of_fabric))}${item.unit_name}`,
        process_price: `${formatUnitPriceDiv(Number(item.process_price))}${item.unit_name !== '' ? `元/${item.unit_name}` : 'kg'}`,
        plan_roll: formatTwoDecimalsDiv(Number(item.plan_roll)),
        use_stock_roll: formatTwoDecimalsDiv(Number(item.use_stock_roll)),
      }
    })
    state.allList = summary_detail.map((item: any) => {
      return {
        ...item,
        grey_fabric_gram_weight: item.grey_fabric_gram_weight,
        plan_weight: formatWeightDiv(Number(item.plan_weight)),
        plan_roll: formatTwoDecimalsDiv(Number(item.plan_roll)),
        use_stock_roll: formatTwoDecimalsDiv(Number(item.use_stock_roll)),
      }
    })
    state.form = {
      ...data.value,
      receipt_grey_fabric_date: formatDate(data.value.receipt_grey_fabric_date),
    }
    state.order_no = data.value.order_no
    state.status_name = data.value.status_name
    state.status = data.value.status
  }
  else {
    ElMessage.error(detailMsg.value)
  }
}

// 坯布信息表格配置
const columnList_fabic_config = ref({
  fieldApiKey: 'sheetOfProductionPlanDetail',
  showSlotNums: false,
  footerMethod: (val: any) => FooterMethod(val),
})
function FooterMethod({ columns, data }: any) {
  const footerData = [
    columns.map((column: any, columnIndex: number) => {
      if (columnIndex === 0)
        return '汇总'

      if (['plan_roll'].includes(column.field))
        return sumNum(data, 'plan_roll', '匹')

      if (['plan_weight'].includes(column.field))
        return sumNum(data, 'plan_weight', '', 'float')

      if (['use_stock_roll'].includes(column.field))
        return sumNum(data, 'use_stock_roll', '匹')

      return null
    }),
  ]
  return footerData
}
// 坯布信息表格列配置
const columnList_fabic = ref([
  {
    field: 'sale_plan_order_no',
    title: '销售计划单号',
    fixed: 'left',
    minWidth: 100,
  },
  {
    field: 'sale_plan_order_item_no',
    title: '销售计划详情单号',
    minWidth: 150,
  },
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    fixed: 'left',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    fixed: 'left',
    minWidth: 100,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    fixed: 'left',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_width_and_unit_name',
    title: '坯布幅宽',
    fixed: 'left',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_gram_weight_and_unit_name',
    title: '坯布克重',
    minWidth: 100,
  },
  {
    field: 'finish_product_width_and_unit_name',
    title: '成品幅宽',
    minWidth: 100,
  },
  {
    field: 'finish_product_gram_weight_and_unit_name',
    title: '成品克重',
    minWidth: 100,
  },
  {
    field: 'weight_of_fabric',
    title: '布匹定重',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_color_name',
    title: '织坯颜色',
    minWidth: 100,
  },
  {
    field: 'process_price',
    title: '加工单价',
    minWidth: 100,
  },
  {
    field: 'production_plan_type_name',
    title: '计划类型',
    minWidth: 100,
  },
  {
    field: 'plan_roll',
    title: '计划匹数',
    minWidth: 100,
  },
  {
    field: 'plan_weight',
    soltName: 'plan_weight',
    title: '计划数量',
    minWidth: 100,
    fixed: 'right',
  },
  {
    field: 'use_stock_roll',
    title: '调库存匹数',
    minWidth: 100,
    fixed: 'right',
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 100,
    fixed: 'right',
  },
])

// 汇总
const columnList_all_config = ref({
  fieldApiKey: 'sheetOfProductionPlanDetail_B',
  showSlotNums: false,
})
const columnList_all = ref([
  {
    field: 'grey_fabric_code',
    title: '坯布编号',
    fixed: 'left',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_name',
    title: '坯布名称',
    fixed: 'left',
    minWidth: 100,
  },
  {
    field: 'customer_name',
    title: '所属客户',
    fixed: 'left',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_width_and_unit_name',
    title: '坯布幅宽',
    fixed: 'left',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_gram_weight_and_unit_name',
    title: '坯布克重',
    minWidth: 100,
  },
  {
    field: 'unit_name',
    title: '单位',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_color_name',
    title: '织坯颜色',
    minWidth: 100,
  },
  {
    field: 'plan_roll',
    title: '计划匹数',
    minWidth: 100,
  },
  {
    field: 'plan_weight',
    soltName: 'plan_weight',
    title: '计划数量',
    minWidth: 100,
    fixed: 'right',
  },
  {
    field: 'use_stock_roll',
    title: '调库存匹数',
    minWidth: 100,
    fixed: 'right',
  },
])

// 作废

const { fetchData: deleteCancelFetch, success: deleteCancelSuccess, msg: deleteCancelMsg } = deleteCancelSheetOfProductionPlan()
async function cancel(status: number) {
  const res = await deleteToast('确认作废嘛？')
  if (res) {
    await deleteCancelFetch({ status, id: route.query.id })
    if (deleteCancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(deleteCancelMsg.value)
    }
  }
}
// 驳回
const { fetchData: rejectFetch, success: rejectSuccess, msg: rejectMsg } = rejectSheetOfProductionPlan()
async function reject(status: number) {
  const res = await deleteToast('确认驳回嘛？')
  if (res) {
    await rejectFetch({ status, id: route.query.id })
    if (rejectSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(rejectMsg.value)
    }
  }
}
// 审核
const { fetchData: auditlFetch, success: auditlSuccess, msg: auditlMsg } = checkSheetOfProductionPlan()
async function audit(status: number) {
  await auditlFetch({ status, id: route.query.id })
  if (auditlSuccess.value) {
    ElMessage.success('成功')
    getData()
  }
  else {
    ElMessage.error(auditlMsg.value)
  }
}
// 消审
const { fetchData: cancelFetch, success: cancelSuccess, msg: cancelMsg } = cancelApprovedSheetOfProductionPlan()
async function eliminate(status: number) {
  const res = await deleteToast('确认取消审核嘛？')
  if (res) {
    await cancelFetch({ status, id: route.query.id })
    if (cancelSuccess.value) {
      ElMessage.success('成功')
      getData()
    }
    else {
      ElMessage.error(cancelMsg.value)
    }
  }
}
</script>

<template>
  <StatusColumn
    :order_no="state.order_no"
    :order_id="Number(route.query.id)"
    :status_name="state.status_name"
    :status="state.status"
    permission_wait_key="SheetOfProductionPlan_wait"
    permission_reject_key="SheetOfProductionPlan_reject"
    permission_pass_key="SheetOfProductionPlan_pass"
    permission_cancel_key="SheetOfProductionPlan_cancel"
    permission_edit_key="SheetOfProductionPlan_edit"
    edit_router_name="SheetOfProductionPlanEdit"
    @cancel="cancel"
    @audit="audit"
    @reject="reject"
    @eliminate="eliminate"
  />
  <FildCard class="mt-[5px]" :tool-bar="false" title="基础信息">
    <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
      <DescriptionsFormItem v-for="(item, index) in form_options" :key="index" :label="`${item.text}:`" :copies="item.copies || 1">
        <template #content>
          {{ state.form[item.key] }}
        </template>
      </DescriptionsFormItem>
    </div>
  </FildCard>
  <FildCard class="mt-[5px]" title="坯布信息">
    <Table :config="columnList_fabic_config" :table-list="state.greyList" :column-list="columnList_fabic">
      <template #plan_weight="{ row }">
        {{ Number(row.plan_weight).toFixed(2) }}{{ row.unit_name }}
      </template>
      <template #grey_fabric_width="{ row }">
        {{ row.grey_fabric_width }} {{ row.grey_fabric_width_unit_name }}
      </template>
      <template #grey_fabric_gram_weight="{ row }">
        {{ row.grey_fabric_gram_weight }} {{ row.grey_fabric_gram_weight_unit_name }}
      </template>
      <template #finish_product_width="{ row }">
        {{ row.finish_product_width }} {{ row.finish_product_width_unit_name }}
      </template>
      <template #finish_product_gram_weight="{ row }">
        {{ row.finish_product_gram_weight }} {{ row.finish_product_gram_weight_unit_name }}
      </template>
    </Table>
  </FildCard>
  <FildCard class="mt-[5px]" title="汇总">
    <Table :config="columnList_all_config" :table-list="state.allList" :column-list="columnList_all">
      <template #plan_weight="{ row }">
        {{ Number(row.plan_weight).toFixed(2) }}{{ row.unit_name }}
      </template>
      <template #grey_fabric_width="{ row }">
        {{ row.grey_fabric_width }} {{ row.grey_fabric_width_unit_name }}
      </template>
      <template #grey_fabric_gram_weight="{ row }">
        {{ row.grey_fabric_gram_weight }} {{ row.grey_fabric_gram_weight_unit_name }}
      </template>
    </Table>
  </FildCard>
</template>

<style lang="scss" scoped>
.form_container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  color: #666;

  .form_container_item {
    display: flex;
    margin-bottom: 15px;

    .label {
      color: #ccc;
      margin-right: 10px;
    }
  }
}

.cols {
  display: flex;
  margin-bottom: 20px;

  .col_item {
    width: 20%;
    text-align: center;

    .label {
      font-weight: 700;
    }
  }
}
</style>
